"""
Research Agent WebSocket Integration

This module provides WebSocket endpoints for real-time communication between
the frontend and the Research Agent. It handles:

- Real-time progress notifications during research tasks
- Citation highlighting with confidence indicators
- Human oversight alerts and review prompts
- Task status updates and completion notifications
- Error handling and connection management

Key Features:
- JWT authentication for WebSocket connections
- Tenant isolation for multi-tenant security
- Connection management with automatic cleanup
- Real-time progress broadcasting
- Error handling and reconnection support
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import asdict

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.security import HTTPBearer

from backend.agents.interactive.research.ui_integration import (
    UINotification,
    NotificationType,
    AlertSeverity,
    TaskProgress,
    ui_manager
)

# Configure logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/ws", tags=["websocket"])

# Security scheme
security = HTTPBearer()


class ConnectionManager:
    """Manages WebSocket connections for research notifications."""
    
    def __init__(self):
        # Store connections by user_id and thread_id
        self.active_connections: Dict[str, Dict[str, List[WebSocket]]] = {}
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, thread_id: str, tenant_id: str):
        """Accept a new WebSocket connection and register it."""
        await websocket.accept()
        
        # Initialize user connections if not exists
        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}
        
        # Initialize thread connections if not exists
        if thread_id not in self.active_connections[user_id]:
            self.active_connections[user_id][thread_id] = []
        
        # Add connection
        self.active_connections[user_id][thread_id].append(websocket)
        
        # Store metadata
        self.connection_metadata[websocket] = {
            "user_id": user_id,
            "thread_id": thread_id,
            "tenant_id": tenant_id
        }
        
        logger.info(f"WebSocket connected: user={user_id}, thread={thread_id}, tenant={tenant_id}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection and clean up."""
        if websocket not in self.connection_metadata:
            return
        
        metadata = self.connection_metadata[websocket]
        user_id = metadata["user_id"]
        thread_id = metadata["thread_id"]
        
        # Remove from active connections
        if (user_id in self.active_connections and 
            thread_id in self.active_connections[user_id] and
            websocket in self.active_connections[user_id][thread_id]):
            
            self.active_connections[user_id][thread_id].remove(websocket)
            
            # Clean up empty lists/dicts
            if not self.active_connections[user_id][thread_id]:
                del self.active_connections[user_id][thread_id]
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        # Remove metadata
        del self.connection_metadata[websocket]
        
        logger.info(f"WebSocket disconnected: user={user_id}, thread={thread_id}")
    
    async def send_to_user_thread(self, user_id: str, thread_id: str, message: Dict[str, Any]):
        """Send a message to all connections for a specific user and thread."""
        if (user_id in self.active_connections and 
            thread_id in self.active_connections[user_id]):
            
            connections = self.active_connections[user_id][thread_id].copy()
            
            for connection in connections:
                try:
                    await connection.send_json(message)
                except Exception as e:
                    logger.error(f"Failed to send message to WebSocket: {e}")
                    # Remove failed connection
                    self.disconnect(connection)
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Send a message to all connections for a specific user."""
        if user_id in self.active_connections:
            for thread_id in list(self.active_connections[user_id].keys()):
                await self.send_to_user_thread(user_id, thread_id, message)
    
    def get_connection_count(self) -> int:
        """Get total number of active connections."""
        count = 0
        for user_connections in self.active_connections.values():
            for thread_connections in user_connections.values():
                count += len(thread_connections)
        return count


# Global connection manager
manager = ConnectionManager()


async def authenticate_websocket(token: str) -> Dict[str, Any]:
    """
    Authenticate WebSocket connection using JWT token.
    
    Args:
        token: JWT token from query parameter
        
    Returns:
        Dict containing user information
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Import here to avoid circular imports
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))
        from pi_lawyer.auth.jwt_auth import JWTAuth
        
        jwt_auth = JWTAuth()
        token_data = jwt_auth.decode_token(token)
        
        return {
            "user_id": token_data["sub"],
            "tenant_id": token_data["tenant_id"],
            "role": token_data["role"],
            "email": token_data["email"]
        }
    except Exception as e:
        logger.error(f"WebSocket authentication failed: {e}")
        raise HTTPException(status_code=401, detail="Authentication failed")


@router.websocket("/research/{thread_id}")
async def research_websocket_endpoint(
    websocket: WebSocket,
    thread_id: str,
    token: str = Query(..., description="JWT authentication token")
):
    """
    WebSocket endpoint for real-time research progress notifications.
    
    This endpoint provides real-time updates for research tasks including:
    - Progress updates during multi-step evaluation
    - Citation highlighting with confidence scores
    - Human oversight alerts
    - Task completion notifications
    - Error notifications
    
    Args:
        websocket: WebSocket connection
        thread_id: Research thread identifier
        token: JWT authentication token
    """
    try:
        # Authenticate the connection
        user_info = await authenticate_websocket(token)
        user_id = user_info["user_id"]
        tenant_id = user_info["tenant_id"]
        
        # Connect to manager
        await manager.connect(websocket, user_id, thread_id, tenant_id)
        
        # Send welcome message
        await websocket.send_json({
            "type": "connection_established",
            "message": "Connected to research notifications",
            "thread_id": thread_id,
            "user_id": user_id,
            "timestamp": asyncio.get_event_loop().time()
        })
        
        # Keep connection alive and handle incoming messages
        try:
            while True:
                # Wait for messages from client (heartbeat, etc.)
                data = await websocket.receive_text()
                
                # Handle client messages
                try:
                    message = json.loads(data)
                    await handle_client_message(websocket, message, user_id, thread_id)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from client: {data}")
                except Exception as e:
                    logger.error(f"Error handling client message: {e}")
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected normally: user={user_id}, thread={thread_id}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            manager.disconnect(websocket)
            
    except HTTPException as e:
        logger.error(f"WebSocket authentication failed: {e.detail}")
        await websocket.close(code=1008, reason="Authentication failed")
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close(code=1011, reason="Internal server error")


async def handle_client_message(
    websocket: WebSocket, 
    message: Dict[str, Any], 
    user_id: str, 
    thread_id: str
):
    """
    Handle messages received from the client.
    
    Args:
        websocket: WebSocket connection
        message: Parsed message from client
        user_id: User identifier
        thread_id: Thread identifier
    """
    message_type = message.get("type")
    
    if message_type == "ping":
        # Respond to ping with pong
        await websocket.send_json({
            "type": "pong",
            "timestamp": asyncio.get_event_loop().time()
        })
    elif message_type == "get_status":
        # Send current research status
        await send_research_status(websocket, user_id, thread_id)
    else:
        logger.warning(f"Unknown message type: {message_type}")


async def send_research_status(websocket: WebSocket, user_id: str, thread_id: str):
    """
    Send current research status to the client.
    
    Args:
        websocket: WebSocket connection
        user_id: User identifier
        thread_id: Thread identifier
    """
    try:
        # Get current research status from async manager
        from backend.agents.interactive.research.async_manager import async_manager
        
        # Get active tasks for this thread
        # This is a simplified implementation - you might want to enhance this
        status = {
            "type": "status_update",
            "thread_id": thread_id,
            "active_tasks": [],  # Would be populated from async_manager
            "connection_count": manager.get_connection_count(),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await websocket.send_json(status)
        
    except Exception as e:
        logger.error(f"Failed to send research status: {e}")


# Integration with UI Manager
class WebSocketUIManager:
    """Integrates WebSocket notifications with the UI manager."""
    
    async def send_notification(self, notification: UINotification):
        """Send notification via WebSocket to connected clients."""
        message = {
            "type": "notification",
            "notification": asdict(notification),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        # Send to specific user and thread
        await manager.send_to_user_thread(
            notification.user_id,
            notification.thread_id,
            message
        )
    
    async def send_progress_update(self, user_id: str, thread_id: str, progress: TaskProgress):
        """Send progress update via WebSocket."""
        message = {
            "type": "progress_update",
            "progress": asdict(progress),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await manager.send_to_user_thread(user_id, thread_id, message)


# Initialize WebSocket UI manager
websocket_ui_manager = WebSocketUIManager()

# Register with the global UI manager
ui_manager.register_websocket_handler(websocket_ui_manager)


@router.get("/research/status")
async def get_websocket_status():
    """Get WebSocket connection status for monitoring."""
    return {
        "active_connections": manager.get_connection_count(),
        "users_connected": len(manager.active_connections),
        "status": "healthy"
    }
