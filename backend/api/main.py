"""
Main FastAPI application entry point.

This module initializes the FastAPI application and registers all routers.
"""

# Ensure .env is loaded before importing any route or service modules
from dotenv import load_dotenv

# Load .env and override any existing vars to pick up updated values
load_dotenv(override=True)

import logging
import os

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response

# from backend.metrics import get_metrics_output

# Import metrics components
from backend.middleware.metrics_middleware import CalendarMetricsMiddleware
from backend.middleware.deprecation_middleware import DeprecationMiddleware

# Import webhook retry task
from backend.tasks.webhook_retry import schedule_webhook_retry_task

# Import all route modules
from .copilotkit_route import router as copilotkit_router
from .routes import scheduling_router, webhook_router
from .routes.admin import llm_router
from .routes.avricons_intake import router as avricons_intake_router
from .routes.calendar import router as calendar_router
from .routes.deadline_insights import router as deadline_insights_router
from .routes.deprecation_monitoring import router as deprecation_monitoring_router
from .routes.jobs import router as jobs_router
from .routes.mcp_metrics import router as mcp_metrics_router
from .routes.tenant_briefing_config import router as tenant_briefing_config_router
from .routes.tenants import router as tenants_router
from .routes.research_websocket import router as research_websocket_router
from .subscription_api import router as subscription_router

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Initialization function to create the FastAPI app
def create_app():
    # Create FastAPI app
    app = FastAPI(
        title="AiLex API",
        description="API for AiLex Legal Assistant",
        version="1.0.0",
    )

    # Configure CORS
    origins = os.getenv("CORS_ORIGINS", "*").split(",")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add metrics middleware for calendar routes
    app.add_middleware(CalendarMetricsMiddleware)

    # Add deprecation middleware for deprecated API endpoints
    app.add_middleware(DeprecationMiddleware)

    # Register routers
    app.include_router(copilotkit_router)
    app.include_router(webhook_router)
    app.include_router(scheduling_router)
    app.include_router(llm_router)
    app.include_router(avricons_intake_router)
    app.include_router(calendar_router)
    app.include_router(deadline_insights_router)
    app.include_router(deprecation_monitoring_router)
    app.include_router(jobs_router)
    app.include_router(mcp_metrics_router)
    app.include_router(tenant_briefing_config_router)
    app.include_router(tenants_router)
    app.include_router(research_websocket_router)
    app.include_router(subscription_router)

    # Add startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        import time
        app.state.start_time = time.time()
        logger.info("Starting AiLex API")

        # Validate security configuration at startup
        try:
            # Import here to avoid circular imports
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
            from pi_lawyer.security import validate_startup_security
            validate_startup_security()
            logger.info("✅ Security validation passed")
        except Exception as e:
            logger.error(f"❌ Security validation failed: {e}")
            logger.error("Consider fixing security configuration issues")
            # Don't block startup for backend API, but log the issues

    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Shutting down AiLex API")

    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint that returns a simple message."""
        return {"message": "PI Lawyer AI API is running"}

    # Add health endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring and container health checks."""
        from datetime import datetime
        import time
        import platform

        return {
            "status": "ok",
            "version": "1.0.0",
            "environment": os.environ.get("APP_ENV", "development"),
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": int(time.time() - app.state.start_time) if hasattr(app.state, 'start_time') else 0,
            "system_info": {
                "python_version": platform.python_version(),
                "system": platform.system(),
                "platform": platform.platform()
            }
        }

    # Add metrics endpoint
    # @app.get("/metrics")
    # async def metrics():
    #     """Prometheus metrics endpoint."""
    #     metrics_output = get_metrics_output()
    #     return Response(
    #         content=metrics_output,
    #         media_type="text/plain; version=0.0.4"
    #     )

    # Register webhook retry task
    schedule_webhook_retry_task(app)

    return app


# Create the app instance to be used by ASGI servers (like Uvicorn)
app = create_app()

# For direct execution (development)
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
