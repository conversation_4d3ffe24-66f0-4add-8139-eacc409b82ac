"""
AiLex Research Agent Self-Correction Logic

This module implements sophisticated self-correction strategies for the Research Agent,
including query reformulation, source verification, and iterative refinement with
ethical compliance safeguards.

Key Features:
- Query reformulation strategies based on evaluation feedback
- Source verification and fact-checking mechanisms
- Iterative refinement with quality improvement
- Ethical compliance checks and human oversight triggers
- Max iteration limits to prevent infinite loops
- Performance tracking and optimization
"""

import asyncio
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from backend.agents.interactive.research.state import ResearchState, Document
from backend.agents.interactive.research.evaluators import (
    ComprehensiveEvaluation,
    EvaluationMetric,
    EvaluationResult
)
from backend.services.laws_api_client import LawsApiClient, SearchRequest
from shared.core.llm import LLMConfig, LLMMessage, generate_completion

# Configure logger
logger = logging.getLogger(__name__)


class RefinementStrategy(Enum):
    """Types of refinement strategies available."""
    QUERY_REFORMULATION = "query_reformulation"
    SOURCE_VERIFICATION = "source_verification"
    CITATION_ENHANCEMENT = "citation_enhancement"
    LEGAL_ACCURACY_CHECK = "legal_accuracy_check"
    ETHICAL_COMPLIANCE_FIX = "ethical_compliance_fix"


@dataclass
class RefinementAction:
    """Represents a specific refinement action to be applied."""
    strategy: RefinementStrategy
    description: str
    parameters: Dict[str, Any]
    priority: int  # 1 = highest priority
    estimated_impact: float  # 0.0 to 1.0


class SelfCorrectionEngine:
    """
    Advanced self-correction engine for the Research Agent.
    
    Implements sophisticated refinement strategies to improve research quality
    through iterative enhancement and verification.
    """
    
    def __init__(self, model: str = "gpt-4o"):
        """
        Initialize the self-correction engine.
        
        Args:
            model: LLM model to use for refinement
        """
        self.model = model
        self.max_refinement_actions = 3  # Limit actions per iteration
        
    async def generate_refinement_plan(
        self,
        state: ResearchState,
        evaluation: ComprehensiveEvaluation,
        iteration: int
    ) -> List[RefinementAction]:
        """
        Generate a comprehensive refinement plan based on evaluation results.
        
        Args:
            state: Current research state
            evaluation: Evaluation results
            iteration: Current iteration number
            
        Returns:
            List of refinement actions prioritized by impact
        """
        logger.info(f"Generating refinement plan for iteration {iteration}")
        
        actions = []
        
        # Analyze each evaluation metric and generate appropriate actions
        for metric, result in evaluation.individual_scores.items():
            if result.score < 0.7:  # Below good threshold
                metric_actions = await self._generate_metric_specific_actions(
                    metric, result, state, iteration
                )
                actions.extend(metric_actions)
        
        # Sort actions by priority and estimated impact
        actions.sort(key=lambda x: (x.priority, -x.estimated_impact))
        
        # Limit to max actions to prevent overwhelming refinement
        return actions[:self.max_refinement_actions]
    
    async def apply_refinement_actions(
        self,
        state: ResearchState,
        actions: List[RefinementAction],
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply refinement actions to improve the research state.
        
        Args:
            state: Research state to refine
            actions: List of refinement actions
            iteration: Current iteration number
            
        Returns:
            Summary of applied refinements
        """
        applied_refinements = []
        
        for action in actions:
            try:
                logger.info(f"Applying refinement: {action.description}")
                
                if action.strategy == RefinementStrategy.QUERY_REFORMULATION:
                    result = await self._apply_query_reformulation(state, action, iteration)
                elif action.strategy == RefinementStrategy.SOURCE_VERIFICATION:
                    result = await self._apply_source_verification(state, action, iteration)
                elif action.strategy == RefinementStrategy.CITATION_ENHANCEMENT:
                    result = await self._apply_citation_enhancement(state, action, iteration)
                elif action.strategy == RefinementStrategy.LEGAL_ACCURACY_CHECK:
                    result = await self._apply_legal_accuracy_check(state, action, iteration)
                elif action.strategy == RefinementStrategy.ETHICAL_COMPLIANCE_FIX:
                    result = await self._apply_ethical_compliance_fix(state, action, iteration)
                else:
                    result = {"success": False, "error": f"Unknown strategy: {action.strategy}"}
                
                if result.get("success", False):
                    applied_refinements.append({
                        "strategy": action.strategy.value,
                        "description": action.description,
                        "result": result
                    })
                else:
                    logger.warning(f"Refinement failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                logger.error(f"Error applying refinement {action.description}: {str(e)}")
        
        return {
            "applied_count": len(applied_refinements),
            "refinements": applied_refinements,
            "iteration": iteration
        }
    
    async def _generate_metric_specific_actions(
        self,
        metric: EvaluationMetric,
        result: EvaluationResult,
        state: ResearchState,
        iteration: int
    ) -> List[RefinementAction]:
        """
        Generate refinement actions specific to an evaluation metric.
        
        Args:
            metric: Evaluation metric that needs improvement
            result: Evaluation result for the metric
            state: Current research state
            iteration: Current iteration number
            
        Returns:
            List of metric-specific refinement actions
        """
        actions = []
        
        if metric == EvaluationMetric.FAITHFULNESS:
            if result.score < 0.5:
                actions.append(RefinementAction(
                    strategy=RefinementStrategy.SOURCE_VERIFICATION,
                    description="Verify all claims against source documents",
                    parameters={"strict_verification": True},
                    priority=1,
                    estimated_impact=0.8
                ))
            
            actions.append(RefinementAction(
                strategy=RefinementStrategy.QUERY_REFORMULATION,
                description="Reformulate queries for better source alignment",
                parameters={"focus": "faithfulness", "specificity": "high"},
                priority=2,
                estimated_impact=0.6
            ))
        
        elif metric == EvaluationMetric.HALLUCINATION:
            actions.append(RefinementAction(
                strategy=RefinementStrategy.SOURCE_VERIFICATION,
                description="Remove unsupported claims and verify facts",
                parameters={"hallucination_check": True, "conservative_mode": True},
                priority=1,
                estimated_impact=0.9
            ))
        
        elif metric == EvaluationMetric.CITATION_QUALITY:
            actions.append(RefinementAction(
                strategy=RefinementStrategy.CITATION_ENHANCEMENT,
                description="Improve citation format and completeness",
                parameters={"format_check": True, "completeness_check": True},
                priority=2,
                estimated_impact=0.7
            ))
        
        elif metric == EvaluationMetric.LEGAL_ACCURACY:
            actions.append(RefinementAction(
                strategy=RefinementStrategy.LEGAL_ACCURACY_CHECK,
                description="Verify legal reasoning and jurisdiction compliance",
                parameters={"jurisdiction": state.jurisdiction, "practice_areas": list(state.practice_areas)},
                priority=1,
                estimated_impact=0.8
            ))
        
        elif metric == EvaluationMetric.ETHICAL_COMPLIANCE:
            actions.append(RefinementAction(
                strategy=RefinementStrategy.ETHICAL_COMPLIANCE_FIX,
                description="Add ethical disclaimers and compliance checks",
                parameters={"aba_compliance": True, "disclaimer_check": True},
                priority=1,
                estimated_impact=0.6
            ))
        
        return actions
    
    async def _apply_query_reformulation(
        self,
        state: ResearchState,
        action: RefinementAction,
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply query reformulation refinement.
        
        Args:
            state: Research state
            action: Refinement action
            iteration: Current iteration
            
        Returns:
            Refinement result
        """
        try:
            focus = action.parameters.get("focus", "general")
            specificity = action.parameters.get("specificity", "medium")
            
            reformulation_prompt = f"""
            Reformulate the following research queries to improve {focus} and increase {specificity} specificity.
            
            Original question: {state.question}
            Current queries: {state.queries}
            Jurisdiction: {state.jurisdiction}
            
            Generate 3-5 improved queries that:
            1. Are more specific and targeted
            2. Focus on {focus} improvement
            3. Are appropriate for {state.jurisdiction} jurisdiction
            4. Will yield more relevant and accurate results
            
            Return as JSON array: ["query1", "query2", "query3"]
            """
            
            messages = [
                LLMMessage(role="system", content="You are an expert at formulating precise legal research queries."),
                LLMMessage(role="user", content=reformulation_prompt)
            ]
            
            response = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.3)
            )
            
            # Parse new queries
            import json
            query_match = re.search(r'\[.*\]', response.content, re.DOTALL)
            if query_match:
                new_queries = json.loads(query_match.group())
                state.queries = new_queries[:5]  # Limit to 5 queries
                
                # Mark for re-search
                state.search_metadata[f"iteration_{iteration}_reformulated"] = True
                
                return {
                    "success": True,
                    "new_queries": new_queries,
                    "original_count": len(state.queries),
                    "new_count": len(new_queries)
                }
            else:
                return {"success": False, "error": "Could not parse reformulated queries"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _apply_citation_enhancement(
        self,
        state: ResearchState,
        action: RefinementAction,
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply citation enhancement refinement.

        Args:
            state: Research state
            action: Refinement action
            iteration: Current iteration

        Returns:
            Refinement result
        """
        try:
            format_check = action.parameters.get("format_check", True)
            completeness_check = action.parameters.get("completeness_check", True)

            if not state.citations:
                return {"success": False, "error": "No citations to enhance"}

            enhancement_prompt = f"""
            Enhance the following citations for legal research compliance and completeness.

            Current citations:
            {state.citations}

            Jurisdiction: {state.jurisdiction}

            {"Check and fix citation format according to legal standards." if format_check else ""}
            {"Ensure all citations have complete information." if completeness_check else ""}

            Provide enhanced citations as JSON array with format:
            [{{
                "id": citation_number,
                "text": "enhanced citation text",
                "type": "case/statute/regulation/other",
                "jurisdiction": "jurisdiction",
                "completeness_score": 0.0-1.0
            }}]
            """

            messages = [
                LLMMessage(role="system", content="You are an expert in legal citation standards and formatting."),
                LLMMessage(role="user", content=enhancement_prompt)
            ]

            response = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            # Parse enhanced citations
            import json
            json_match = re.search(r'\[.*\]', response.content, re.DOTALL)
            if json_match:
                enhanced_citations = json.loads(json_match.group())

                # Update state citations
                state.citations = enhanced_citations
                state.search_metadata[f"iteration_{iteration}_citations_enhanced"] = True

                avg_completeness = sum(c.get("completeness_score", 0.5) for c in enhanced_citations) / len(enhanced_citations)

                return {
                    "success": True,
                    "enhanced_count": len(enhanced_citations),
                    "average_completeness": avg_completeness
                }
            else:
                return {"success": False, "error": "Could not parse enhanced citations"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _apply_legal_accuracy_check(
        self,
        state: ResearchState,
        action: RefinementAction,
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply legal accuracy check refinement.

        Args:
            state: Research state
            action: Refinement action
            iteration: Current iteration

        Returns:
            Refinement result
        """
        try:
            jurisdiction = action.parameters.get("jurisdiction", state.jurisdiction)
            practice_areas = action.parameters.get("practice_areas", [])

            if not state.answer:
                return {"success": False, "error": "No answer to check for legal accuracy"}

            accuracy_prompt = f"""
            Review the following legal research answer for accuracy and compliance with {jurisdiction} law.

            Answer:
            {state.answer}

            Question: {state.question}
            Jurisdiction: {jurisdiction}
            Practice Areas: {practice_areas}

            Check for:
            1. Legal reasoning accuracy
            2. Jurisdiction-specific law compliance
            3. Proper legal terminology
            4. Logical conclusions
            5. Appropriate disclaimers

            Provide accuracy assessment as JSON:
            {{
                "accuracy_score": 0.0-1.0,
                "legal_issues": ["list of legal accuracy concerns"],
                "jurisdiction_compliance": "compliant/non-compliant/unclear",
                "recommended_fixes": ["specific improvements needed"],
                "requires_attorney_review": true/false
            }}
            """

            messages = [
                LLMMessage(role="system", content=f"You are an expert legal researcher specializing in {jurisdiction} law."),
                LLMMessage(role="user", content=accuracy_prompt)
            ]

            response = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            # Parse accuracy assessment
            import json
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                accuracy_data = json.loads(json_match.group())

                # Store accuracy assessment
                state.search_metadata[f"iteration_{iteration}_legal_accuracy"] = accuracy_data

                # Set flags based on assessment
                accuracy_score = accuracy_data.get("accuracy_score", 0.5)
                if accuracy_score < 0.7:
                    state.low_confidence_classification = True

                if accuracy_data.get("requires_attorney_review", False):
                    state.search_metadata["human_oversight_required"] = True

                return {
                    "success": True,
                    "accuracy_score": accuracy_score,
                    "legal_issues_count": len(accuracy_data.get("legal_issues", [])),
                    "requires_review": accuracy_data.get("requires_attorney_review", False)
                }
            else:
                return {"success": False, "error": "Could not parse accuracy assessment"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _apply_ethical_compliance_fix(
        self,
        state: ResearchState,
        action: RefinementAction,
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply ethical compliance fix refinement.

        Args:
            state: Research state
            action: Refinement action
            iteration: Current iteration

        Returns:
            Refinement result
        """
        try:
            aba_compliance = action.parameters.get("aba_compliance", True)
            disclaimer_check = action.parameters.get("disclaimer_check", True)

            if not state.answer:
                return {"success": False, "error": "No answer to check for ethical compliance"}

            compliance_prompt = f"""
            Review and enhance the following legal research answer for ABA ethical compliance.

            Current answer:
            {state.answer}

            {"Ensure compliance with ABA Model Rules for AI in legal practice." if aba_compliance else ""}
            {"Add appropriate legal disclaimers and limitations." if disclaimer_check else ""}

            Provide enhanced answer with ethical compliance improvements as JSON:
            {{
                "enhanced_answer": "improved answer with ethical compliance",
                "compliance_score": 0.0-1.0,
                "disclaimers_added": ["list of disclaimers added"],
                "ethical_improvements": ["list of ethical enhancements made"],
                "requires_attorney_supervision": true/false
            }}
            """

            messages = [
                LLMMessage(role="system", content="You are an expert in legal ethics and ABA compliance for AI systems."),
                LLMMessage(role="user", content=compliance_prompt)
            ]

            response = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            # Parse compliance enhancement
            import json
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                compliance_data = json.loads(json_match.group())

                # Update answer with ethical enhancements
                enhanced_answer = compliance_data.get("enhanced_answer")
                if enhanced_answer and len(enhanced_answer) > len(state.answer or ""):
                    state.answer = enhanced_answer

                # Store compliance data
                state.search_metadata[f"iteration_{iteration}_ethical_compliance"] = compliance_data

                # Set supervision flag if needed
                if compliance_data.get("requires_attorney_supervision", False):
                    state.search_metadata["human_oversight_required"] = True

                compliance_score = compliance_data.get("compliance_score", 0.5)

                return {
                    "success": True,
                    "compliance_score": compliance_score,
                    "disclaimers_added": len(compliance_data.get("disclaimers_added", [])),
                    "requires_supervision": compliance_data.get("requires_attorney_supervision", False)
                }
            else:
                return {"success": False, "error": "Could not parse compliance enhancement"}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _apply_source_verification(
        self,
        state: ResearchState,
        action: RefinementAction,
        iteration: int
    ) -> Dict[str, Any]:
        """
        Apply source verification refinement.
        
        Args:
            state: Research state
            action: Refinement action
            iteration: Current iteration
            
        Returns:
            Refinement result
        """
        try:
            strict_mode = action.parameters.get("strict_verification", False)
            hallucination_check = action.parameters.get("hallucination_check", False)
            
            if not state.answer:
                return {"success": False, "error": "No answer to verify"}
            
            # Combine source content for verification
            sources = state.legal_documents + state.case_documents
            source_content = "\n\n".join([
                f"Source {i+1}: {doc.page_content[:1000]}"
                for i, doc in enumerate(sources[:10])
            ])
            
            verification_prompt = f"""
            Verify the following answer against the provided sources and identify any unsupported claims.
            
            Answer to verify:
            {state.answer}
            
            Available sources:
            {source_content}
            
            {"Use strict verification - flag any claim not explicitly supported." if strict_mode else "Use reasonable verification standards."}
            {"Focus especially on potential hallucinations." if hallucination_check else ""}
            
            Provide verification results as JSON:
            {{
                "verified_claims": ["list of claims supported by sources"],
                "unsupported_claims": ["list of claims not supported by sources"],
                "verification_score": 0.0-1.0,
                "recommended_action": "accept/revise/reject"
            }}
            """
            
            messages = [
                LLMMessage(role="system", content="You are an expert fact-checker specializing in legal research verification."),
                LLMMessage(role="user", content=verification_prompt)
            ]
            
            response = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )
            
            # Parse verification results
            import json
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                verification_data = json.loads(json_match.group())
                
                # Store verification results
                state.search_metadata[f"iteration_{iteration}_verification"] = verification_data
                
                # Set confidence flags based on verification
                verification_score = verification_data.get("verification_score", 0.5)
                if verification_score < 0.6:
                    state.low_confidence_rerank = True
                
                return {
                    "success": True,
                    "verification_score": verification_score,
                    "unsupported_claims": len(verification_data.get("unsupported_claims", [])),
                    "action_recommended": verification_data.get("recommended_action", "review")
                }
            else:
                return {"success": False, "error": "Could not parse verification results"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
