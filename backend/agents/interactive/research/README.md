# Research Agent

This directory contains the Research Agent implementation for the AiLex system. The Research Agent handles quick research queries and queues longer research tasks.

## Key Features

- Supports AI-powered legal research for solo and small-firm lawyers
- Automatically classifies user queries by type and data source needs
- Supports both legal database search and web search paths
- Provides citation tracking and formatting for search results
- Integrates with CopilotKit Cloud for seamless frontend-backend communication

## Architecture

The agent uses a hybrid retrieval pipeline with:
- Query generation using LLMs
- Legal document search via laws-API service
- Graph expansion using laws-API knowledge graph
- Document recommendations via laws-API
- Reranking with Voyage via Groq
- Web search as an alternative path

## Files

- `agent.py`: The main ResearchAgent class implementation
- `state.py`: State management for the research agent
- `graph.py`: LangGraph workflow definition
- `nodes.py`: Node implementations for the LangGraph workflow
- `test_agent.py`: Tests for the Research Agent

## Usage

The Research Agent is designed to be used as part of the AiLex agent architecture. It can be invoked directly or through the master router.

```python
from backend.agents.interactive.research.agent import ResearchAgent
from shared.core.state import AiLexState

# Create a state with a user question
state = AiLexState(
    user_context={
        "user_id": "user-123",
        "tenant_id": "tenant-456",
        "role": "attorney",
        "assigned_case_ids": ["case-789"],
        "settings": {}
    },
    messages=[
        {
            "role": "user",
            "content": "What are the requirements for a personal injury claim in Texas?",
            "metadata": {}
        }
    ]
)

# Create and invoke the agent
agent = ResearchAgent()
result = await agent(state)

# Get the answer from the result
answer = next((m["content"] for m in result.messages if m["role"] == "assistant"), None)
print(answer)
```

## Testing

To run the tests for the Research Agent, use pytest:

```bash
pytest backend/agents/interactive/research/test_agent.py
```

## Integration with Master Router

The Research Agent is integrated with the master router through the LangGraph StateGraph. The router examines the state and routes to the Research Agent when appropriate.

## Future Improvements

- Implement full integration with Pinecone for vector search
- Implement full integration with Neo4j for graph expansion
- Implement full integration with Voyage Rerank via Groq for reranking
- Add support for more jurisdictions beyond Texas
- Enhance citation formatting and tracking
- Improve performance for sub-second response times
