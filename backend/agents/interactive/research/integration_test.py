#!/usr/bin/env python3
"""
Integration test for the AiLex Research Agent Agentic Implementation

This script performs a comprehensive integration test of the agentic wrapper
to verify that all components work together correctly.

Usage:
    python integration_test.py [--verbose] [--quick]
"""

import asyncio
import logging
import sys
import time
from typing import Dict, Any
import argparse

# Add the project root to the path
sys.path.append('/Users/<USER>/CascadeProjects/pi_lawyer_ai')

from backend.agents.interactive.research.state import ResearchState, UserContext

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegrationTester:
    """Integration tester for the agentic research system."""
    
    def __init__(self, verbose: bool = False, quick: bool = False):
        """
        Initialize the integration tester.
        
        Args:
            verbose: Enable verbose logging
            quick: Run quick tests only
        """
        self.verbose = verbose
        self.quick = quick
        self.results = {}
        
        if verbose:
            logging.getLogger().setLevel(logging.DEBUG)
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all integration tests.
        
        Returns:
            Dict containing test results
        """
        logger.info("🚀 Starting AiLex Research Agent Agentic Integration Tests")
        
        tests = [
            ("State Management", self.test_state_management),
            ("Component Imports", self.test_component_imports),
        ]
        
        if not self.quick:
            tests.extend([
                ("Evaluation Framework", self.test_evaluation_framework),
                ("Self-Correction", self.test_self_correction),
                ("Monitoring", self.test_monitoring),
                ("UI Integration", self.test_ui_integration),
            ])
        
        for test_name, test_func in tests:
            logger.info(f"🧪 Running test: {test_name}")
            try:
                start_time = time.time()
                result = await test_func()
                duration = time.time() - start_time
                
                self.results[test_name] = {
                    "status": "PASS" if result else "FAIL",
                    "duration": duration,
                    "details": result if isinstance(result, dict) else {}
                }
                
                status_emoji = "✅" if result else "❌"
                logger.info(f"{status_emoji} {test_name}: {self.results[test_name]['status']} ({duration:.2f}s)")
                
            except Exception as e:
                self.results[test_name] = {
                    "status": "ERROR",
                    "duration": 0,
                    "error": str(e)
                }
                logger.error(f"❌ {test_name}: ERROR - {str(e)}")
        
        return self.results
    
    async def test_component_imports(self) -> bool:
        """Test that all agentic components can be imported successfully."""
        try:
            # Test agentic wrapper import
            from backend.agents.interactive.research.agentic_wrapper import (
                AgenticResearchWrapper, agentic_research_wrapper
            )

            # Test evaluators import
            from backend.agents.interactive.research.evaluators import (
                ResearchEvaluator, EvaluationMetric
            )

            # Test self-correction import
            from backend.agents.interactive.research.self_correction import (
                SelfCorrectionEngine, RefinementStrategy
            )

            # Test monitoring import
            from backend.agents.interactive.research.monitoring import (
                ResearchAgentMonitor, MetricType
            )

            # Test UI integration import
            from backend.agents.interactive.research.ui_integration import (
                UIIntegrationManager, NotificationType
            )

            logger.info("✓ All agentic components imported successfully")
            return True

        except Exception as e:
            logger.error(f"Component import failed: {str(e)}")
            return False
    
    async def test_state_management(self) -> bool:
        """Test ResearchState creation and enhancement methods."""
        try:
            # Create test state
            state = ResearchState(
                query_id="test_integration_001",
                question="What are the liability standards for personal injury in Texas?",
                jurisdiction="texas",
                practice_areas={"personal_injury"},
                user_context=UserContext(
                    user_id="test_user",
                    tenant_id="test_tenant",
                    role="attorney"
                ),
                thread_id="test_thread"
            )
            
            # Test enhanced methods
            eval_metadata = state.get_evaluation_metadata()
            quality_summary = state.get_quality_summary()
            oversight_required = state.requires_human_oversight()
            
            # Verify methods work
            assert isinstance(eval_metadata, dict)
            assert isinstance(quality_summary, dict)
            assert isinstance(oversight_required, bool)
            
            # Test setting evaluation results
            test_eval_data = {"test_score": 0.85, "test_metric": "passed"}
            state.set_evaluation_result(test_eval_data)
            
            updated_metadata = state.get_evaluation_metadata()
            assert "evaluation_results" in state.search_metadata
            
            logger.info("✓ State management methods working correctly")
            return True
            
        except Exception as e:
            logger.error(f"State management test failed: {str(e)}")
            return False
    

    
    async def test_evaluation_framework(self) -> bool:
        """Test the evaluation framework components."""
        try:
            from backend.agents.interactive.research.evaluators import (
                ResearchEvaluator, EvaluationMetric, EvaluationResult
            )
            
            # Test evaluator creation
            evaluator = ResearchEvaluator(model="gpt-4o")
            assert evaluator is not None
            
            # Test evaluation result creation
            test_result = EvaluationResult(
                metric=EvaluationMetric.FAITHFULNESS,
                score=0.85,
                explanation="Test evaluation",
                details={"test": True},
                requires_human_review=False
            )
            
            assert test_result.score == 0.85
            assert test_result.metric == EvaluationMetric.FAITHFULNESS
            
            logger.info("✓ Evaluation framework components working")
            return True
            
        except Exception as e:
            logger.error(f"Evaluation framework test failed: {str(e)}")
            return False
    
    async def test_self_correction(self) -> bool:
        """Test the self-correction engine components."""
        try:
            from backend.agents.interactive.research.self_correction import (
                SelfCorrectionEngine, RefinementStrategy, RefinementAction
            )
            
            # Test engine creation
            engine = SelfCorrectionEngine(model="gpt-4o")
            assert engine is not None
            
            # Test refinement action creation
            action = RefinementAction(
                strategy=RefinementStrategy.QUERY_REFORMULATION,
                description="Test refinement",
                parameters={"test": True},
                priority=1,
                estimated_impact=0.7
            )
            
            assert action.strategy == RefinementStrategy.QUERY_REFORMULATION
            assert action.priority == 1
            
            logger.info("✓ Self-correction engine components working")
            return True
            
        except Exception as e:
            logger.error(f"Self-correction test failed: {str(e)}")
            return False
    
    async def test_monitoring(self) -> bool:
        """Test the monitoring and metrics components."""
        try:
            from backend.agents.interactive.research.monitoring import (
                ResearchAgentMonitor, MetricType, ComplianceLevel
            )
            
            # Test monitor creation
            monitor = ResearchAgentMonitor()
            assert monitor is not None
            
            # Test metric storage
            assert hasattr(monitor, 'metrics_storage')
            assert hasattr(monitor, 'audit_trail')
            
            logger.info("✓ Monitoring components working")
            return True
            
        except Exception as e:
            logger.error(f"Monitoring test failed: {str(e)}")
            return False
    
    async def test_ui_integration(self) -> bool:
        """Test the UI integration components."""
        try:
            from backend.agents.interactive.research.ui_integration import (
                UIIntegrationManager, NotificationType, AlertSeverity
            )
            
            # Test UI manager creation
            ui_manager = UIIntegrationManager()
            assert ui_manager is not None
            
            # Test notification types
            assert NotificationType.PROGRESS_UPDATE is not None
            assert AlertSeverity.INFO is not None
            
            logger.info("✓ UI integration components working")
            return True
            
        except Exception as e:
            logger.error(f"UI integration test failed: {str(e)}")
            return False
    
    def print_summary(self):
        """Print a summary of test results."""
        logger.info("\n" + "="*60)
        logger.info("🎯 INTEGRATION TEST SUMMARY")
        logger.info("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r["status"] == "PASS")
        failed_tests = sum(1 for r in self.results.values() if r["status"] == "FAIL")
        error_tests = sum(1 for r in self.results.values() if r["status"] == "ERROR")
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"💥 Errors: {error_tests}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.info("🎉 Integration tests PASSED - Ready for deployment!")
        elif success_rate >= 60:
            logger.info("⚠️  Integration tests PARTIAL - Some issues need attention")
        else:
            logger.info("🚨 Integration tests FAILED - Major issues need resolution")
        
        # Print detailed results
        logger.info("\nDetailed Results:")
        for test_name, result in self.results.items():
            status_emoji = {"PASS": "✅", "FAIL": "❌", "ERROR": "💥"}[result["status"]]
            logger.info(f"{status_emoji} {test_name}: {result['status']} ({result.get('duration', 0):.2f}s)")
            
            if result["status"] == "ERROR":
                logger.info(f"   Error: {result.get('error', 'Unknown error')}")


async def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="AiLex Research Agent Integration Tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--quick", "-q", action="store_true", help="Run quick tests only")
    
    args = parser.parse_args()
    
    tester = IntegrationTester(verbose=args.verbose, quick=args.quick)
    
    try:
        results = await tester.run_all_tests()
        tester.print_summary()
        
        # Exit with appropriate code
        success_rate = sum(1 for r in results.values() if r["status"] == "PASS") / len(results)
        sys.exit(0 if success_rate >= 0.8 else 1)
        
    except KeyboardInterrupt:
        logger.info("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Test runner failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
