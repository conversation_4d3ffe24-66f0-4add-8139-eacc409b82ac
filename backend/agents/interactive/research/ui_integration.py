"""
AiLex Research Agent UI Integration

This module enhances CopilotKit integration for real-time progress notifications,
citation highlighting, human oversight alerts, and interactive research feedback.

Key Features:
- Real-time progress notifications via WebSocket/SSE
- Citation highlighting with confidence indicators
- Human oversight alerts and review prompts
- Interactive refinement suggestions
- Task status updates and completion notifications
- Error handling and fallback mechanisms
"""

import asyncio

import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from backend.agents.interactive.research.state import ResearchState
from backend.agents.interactive.research.evaluators import ComprehensiveEvaluation
from backend.agents.interactive.research.async_manager import TaskProgress, AsyncResearchTask

# Configure logger
logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Types of UI notifications."""
    PROGRESS_UPDATE = "progress_update"
    CITATION_HIGHLIGHT = "citation_highlight"
    HUMAN_OVERSIGHT_REQUIRED = "human_oversight_required"
    TASK_COMPLETED = "task_completed"
    ERROR_ALERT = "error_alert"
    REFINEMENT_SUGGESTION = "refinement_suggestion"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class UINotification:
    """UI notification message."""
    type: NotificationType
    severity: AlertSeverity
    title: str
    message: str
    data: Dict[str, Any]
    timestamp: float
    user_id: str
    thread_id: str
    requires_action: bool = False
    action_buttons: List[Dict[str, str]] = None


@dataclass
class CitationHighlight:
    """Citation highlighting information."""
    citation_id: int
    text: str
    confidence_score: float
    source_type: str
    jurisdiction: str
    requires_review: bool
    highlight_color: str
    tooltip_text: str


@dataclass
class OversightAlert:
    """Human oversight alert."""
    alert_id: str
    reason: str
    severity: AlertSeverity
    evaluation_scores: Dict[str, float]
    suggested_actions: List[str]
    requires_immediate_attention: bool
    auto_escalate_after: Optional[float] = None


class UIIntegrationManager:
    """
    Manager for real-time UI integration with CopilotKit.

    Handles progress notifications, citation highlighting, oversight alerts,
    and other interactive features for the research agent.
    """

    def __init__(self):
        """Initialize the UI integration manager."""
        self.active_sessions = {}  # Track active user sessions
        self.notification_queue = {}  # Per-user notification queues
        self.websocket_handler: Optional[Any] = None  # WebSocket handler for real-time updates

    async def send_progress_notification(
        self,
        user_id: str,
        thread_id: str,
        progress: TaskProgress
    ) -> None:
        """
        Send real-time progress notification to the UI.

        Args:
            user_id: User identifier
            thread_id: Thread identifier
            progress: Progress update
        """
        notification = UINotification(
            type=NotificationType.PROGRESS_UPDATE,
            severity=AlertSeverity.INFO,
            title="Research Progress",
            message=progress.message,
            data={
                "task_id": progress.task_id,
                "progress": progress.progress,
                "current_step": progress.current_step,
                "iteration": progress.iteration,
                "evaluation_score": progress.evaluation_score
            },
            timestamp=progress.timestamp,
            user_id=user_id,
            thread_id=thread_id
        )

        await self._send_notification(notification)

        # Also send via WebSocket if available
        if self.websocket_handler:
            try:
                await self.websocket_handler.send_progress_update(user_id, thread_id, progress)
            except Exception as e:
                logger.error(f"Failed to send WebSocket progress update: {e}")

    async def send_citation_highlights(
        self,
        user_id: str,
        thread_id: str,
        state: ResearchState,
        evaluation: ComprehensiveEvaluation
    ) -> None:
        """
        Send citation highlighting information to the UI.

        Args:
            user_id: User identifier
            thread_id: Thread identifier
            state: Research state with citations
            evaluation: Evaluation results for confidence scoring
        """
        highlights = []

        for i, citation in enumerate(state.citations):
            # Determine confidence and highlighting
            confidence_score = self._calculate_citation_confidence(citation, evaluation)
            highlight_color = self._get_highlight_color(confidence_score)
            requires_review = confidence_score < 0.7

            highlight = CitationHighlight(
                citation_id=i + 1,
                text=citation.get("text", ""),
                confidence_score=confidence_score,
                source_type=citation.get("type", "unknown"),
                jurisdiction=citation.get("jurisdiction", state.jurisdiction),
                requires_review=requires_review,
                highlight_color=highlight_color,
                tooltip_text=self._generate_citation_tooltip(citation, confidence_score)
            )
            highlights.append(highlight)

        notification = UINotification(
            type=NotificationType.CITATION_HIGHLIGHT,
            severity=AlertSeverity.INFO,
            title="Citation Analysis",
            message=f"Analyzed {len(highlights)} citations",
            data={
                "highlights": [
                    asdict(h) for h in highlights],
                "overall_citation_quality": evaluation.individual_scores.get(
                    "citation_quality",
                    {}).get(
                    "score",
                    0.5)},
            timestamp=asyncio.get_event_loop().time(),
            user_id=user_id,
            thread_id=thread_id)

        await self._send_notification(notification)

    async def send_oversight_alert(
        self,
        user_id: str,
        thread_id: str,
        evaluation: ComprehensiveEvaluation,
        state: ResearchState
    ) -> None:
        """
        Send human oversight alert to the UI.

        Args:
            user_id: User identifier
            thread_id: Thread identifier
            evaluation: Evaluation results
            state: Research state
        """
        if not evaluation.requires_human_oversight:
            return

        # Determine alert severity
        severity = AlertSeverity.WARNING
        if evaluation.overall_score < 0.5:
            severity = AlertSeverity.ERROR
        elif evaluation.overall_score < 0.3:
            severity = AlertSeverity.CRITICAL

        # Generate oversight reasons
        reasons = []
        for metric, result in evaluation.individual_scores.items():
            if result.requires_human_review:
                reasons.append(f"{metric.value}: {result.explanation}")

        alert = OversightAlert(
            alert_id=f"oversight_{state.query_id}",
            reason="; ".join(reasons[:3]),  # Limit to top 3 reasons
            severity=severity,
            evaluation_scores={
                metric.value: result.score
                for metric, result in evaluation.individual_scores.items()
            },
            suggested_actions=evaluation.refinement_suggestions,
            requires_immediate_attention=severity in [
                AlertSeverity.ERROR, AlertSeverity.CRITICAL],
            auto_escalate_after=3600 if severity == AlertSeverity.CRITICAL else None  # 1 hour
        )

        notification = UINotification(
            type=NotificationType.HUMAN_OVERSIGHT_REQUIRED,
            severity=severity,
            title="Human Review Required",
            message=f"Research quality score: {evaluation.overall_score:.1%}",
            data=asdict(alert),
            timestamp=asyncio.get_event_loop().time(),
            user_id=user_id,
            thread_id=thread_id,
            requires_action=True,
            action_buttons=[
                {"label": "Review Now", "action": "review_research"},
                {"label": "Request Refinement", "action": "request_refinement"},
                {"label": "Accept with Disclaimer", "action": "accept_with_disclaimer"}
            ]
        )

        await self._send_notification(notification)

    async def send_task_completion(
        self,
        task: AsyncResearchTask
    ) -> None:
        """
        Send task completion notification.

        Args:
            task: Completed research task
        """
        severity = AlertSeverity.INFO
        title = "Research Completed"
        message = "Your research task has been completed successfully."

        if task.status.value == "failed":
            severity = AlertSeverity.ERROR
            title = "Research Failed"
            message = f"Research task failed: {task.error}"
        elif task.result and task.result.get("requires_oversight", False):
            severity = AlertSeverity.WARNING
            title = "Research Completed - Review Required"
            message = "Research completed but requires human review."

        notification = UINotification(
            type=NotificationType.TASK_COMPLETED,
            severity=severity,
            title=title,
            message=message,
            data={
                "task_id": task.task_id,
                "status": task.status.value,
                "execution_time": task.completed_at - task.started_at if task.started_at else 0,
                "iterations_completed": task.iterations_completed,
                "final_score": task.result.get("evaluation", {}).get("overall_score") if task.result else None
            },
            timestamp=task.completed_at or asyncio.get_event_loop().time(),
            user_id=task.user_id,
            thread_id=task.thread_id,
            requires_action=task.result and task.result.get("requires_oversight", False),
            action_buttons=[
                {"label": "View Results", "action": "view_research_results"},
                {"label": "Download Report", "action": "download_research_report"}
            ] if task.status.value == "completed" else []
        )

        await self._send_notification(notification)

    async def send_refinement_suggestions(
        self,
        user_id: str,
        thread_id: str,
        suggestions: List[str],
        current_score: float
    ) -> None:
        """
        Send interactive refinement suggestions.

        Args:
            user_id: User identifier
            thread_id: Thread identifier
            suggestions: List of refinement suggestions
            current_score: Current quality score
        """
        notification = UINotification(
            type=NotificationType.REFINEMENT_SUGGESTION,
            severity=AlertSeverity.INFO,
            title="Refinement Suggestions",
            message=f"Current quality: {current_score:.1%}. Consider these improvements:",
            data={
                "suggestions": suggestions,
                "current_score": current_score,
                "target_score": 0.9
            },
            timestamp=asyncio.get_event_loop().time(),
            user_id=user_id,
            thread_id=thread_id,
            requires_action=True,
            action_buttons=[
                {"label": "Apply Suggestions", "action": "apply_refinements"},
                {"label": "Manual Review", "action": "manual_review"},
                {"label": "Accept Current", "action": "accept_current"}
            ]
        )

        await self._send_notification(notification)

    def _calculate_citation_confidence(
        self,
        citation: Dict[str, Any],
        evaluation: ComprehensiveEvaluation
    ) -> float:
        """Calculate confidence score for a citation."""
        base_score = 0.7  # Default confidence

        # Adjust based on citation quality evaluation
        citation_eval = evaluation.individual_scores.get("citation_quality")
        if citation_eval:
            base_score = citation_eval.score

        # Adjust based on source type
        source_type = citation.get("type", "").lower()
        if source_type in ["statute", "regulation"]:
            base_score += 0.1
        elif source_type == "case":
            base_score += 0.05

        # Adjust based on jurisdiction match
        if citation.get("jurisdiction") == evaluation.metadata.get("jurisdiction"):
            base_score += 0.05

        return min(1.0, base_score)

    def _get_highlight_color(self, confidence_score: float) -> str:
        """Get highlight color based on confidence score."""
        if confidence_score >= 0.8:
            return "#22c55e"  # Green - high confidence
        elif confidence_score >= 0.6:
            return "#f59e0b"  # Yellow - medium confidence
        else:
            return "#ef4444"  # Red - low confidence

    def _generate_citation_tooltip(
            self, citation: Dict[str, Any], confidence: float) -> str:
        """Generate tooltip text for citation."""
        return f"Confidence: {confidence:.1%} | Type: {citation.get('type', 'Unknown')} | Jurisdiction: {citation.get('jurisdiction', 'Unknown')}"

    async def _send_notification(self, notification: UINotification) -> None:
        """
        Send notification to the UI via CopilotKit/WebSocket.

        Args:
            notification: Notification to send
        """
        try:
            # Send via WebSocket if available
            if self.websocket_handler:
                try:
                    await self.websocket_handler.send_notification(notification)
                    logger.info(f"Sent WebSocket notification: {notification.title}")
                except Exception as e:
                    logger.error(f"Failed to send WebSocket notification: {e}")

            # Log the notification
            logger.info(
                f"UI Notification [{notification.type.value}]: {notification.title} - {notification.message}")

            # Store in queue for polling fallback
            if notification.user_id not in self.notification_queue:
                self.notification_queue[notification.user_id] = []

            self.notification_queue[notification.user_id].append(notification)

            # Keep only last 50 notifications per user
            if len(self.notification_queue[notification.user_id]) > 50:
                self.notification_queue[notification.user_id] = self.notification_queue[notification.user_id][-50:]

        except Exception as e:
            logger.error(f"Failed to send UI notification: {str(e)}")

    async def get_pending_notifications(self, user_id: str) -> List[UINotification]:
        """
        Get pending notifications for a user (polling fallback).

        Args:
            user_id: User identifier

        Returns:
            List of pending notifications
        """
        return self.notification_queue.get(user_id, [])

    async def clear_notifications(self, user_id: str) -> None:
        """
        Clear notifications for a user.

        Args:
            user_id: User identifier
        """
        if user_id in self.notification_queue:
            self.notification_queue[user_id] = []

    def register_websocket_handler(self, handler: Any) -> None:
        """
        Register a WebSocket handler for real-time updates.

        Args:
            handler: WebSocket handler instance
        """
        self.websocket_handler = handler
        logger.info("WebSocket handler registered for real-time updates")


# Global UI integration manager instance
ui_manager = UIIntegrationManager()
