"""
Pytest configuration and shared fixtures for Research Agent tests.

This module provides common test fixtures and configuration for the
Research Agent test suite.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from backend.agents.interactive.research.state import ResearchState, UserContext, Document
from backend.agents.interactive.research.evaluators import (
    ComprehensiveEvaluation,
    EvaluationResult,
    EvaluationMetric
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_user_context():
    """Create a sample user context for testing."""
    return UserContext(
        user_id="test_user_123",
        tenant_id="test_tenant_456",
        role="attorney"
    )


@pytest.fixture
def sample_research_state(sample_user_context):
    """Create a sample research state for testing."""
    return ResearchState(
        query_id="test_query_789",
        question="What are the statute of limitations for personal injury claims in Texas?",
        jurisdiction="texas",
        practice_areas={"personal_injury"},
        user_context=sample_user_context,
        thread_id="test_thread_101",
        queries=[
            "Texas personal injury statute of limitations",
            "limitation period personal injury Texas",
            "time limit file personal injury lawsuit Texas"
        ],
        citations=[
            {
                "id": 1,
                "text": "Texas Civil Practice and Remedies Code § 16.003",
                "type": "statute",
                "jurisdiction": "texas"
            },
            {
                "id": 2,
                "text": "<PERSON> v. Jones, 123 S.W.3d 456 (Tex. 2020)",
                "type": "case",
                "jurisdiction": "texas"
            }
        ],
        legal_documents=[
            Document(
                page_content="Texas Civil Practice and Remedies Code § 16.003 provides that a person must bring suit for personal injury not later than two years after the day the cause of action accrues.",
                metadata={
                    "id": "statute_16_003",
                    "type": "statute",
                    "jurisdiction": "texas",
                    "title": "Texas Civil Practice and Remedies Code § 16.003"
                }
            )
        ],
        case_documents=[
            Document(
                page_content="In Smith v. Jones, the Texas Supreme Court clarified that the statute of limitations begins to run when the plaintiff knew or should have known of the injury and its cause.",
                metadata={
                    "id": "smith_v_jones_2020",
                    "type": "case",
                    "jurisdiction": "texas",
                    "title": "Smith v. Jones",
                    "citation": "123 S.W.3d 456 (Tex. 2020)"
                }
            )
        ],
        search_metadata={
            "search_timestamp": 1234567890,
            "search_duration": 2.5,
            "sources_found": 15,
            "confidence_score": 0.85
        }
    )


@pytest.fixture
def high_quality_evaluation():
    """Create a high-quality evaluation result for testing."""
    individual_scores = {}
    for metric in EvaluationMetric:
        individual_scores[metric] = EvaluationResult(
            metric=metric,
            score=0.9,
            explanation=f"High quality {metric.value} evaluation",
            details={"test": True},
            requires_human_review=False
        )
    
    return ComprehensiveEvaluation(
        overall_score=0.9,
        individual_scores=individual_scores,
        requires_human_oversight=False,
        refinement_suggestions=[],
        metadata={"test_evaluation": True, "quality": "high"}
    )


@pytest.fixture
def low_quality_evaluation():
    """Create a low-quality evaluation result for testing."""
    individual_scores = {}
    for metric in EvaluationMetric:
        score = 0.4 if metric in [EvaluationMetric.FAITHFULNESS, EvaluationMetric.CITATION_QUALITY] else 0.6
        individual_scores[metric] = EvaluationResult(
            metric=metric,
            score=score,
            explanation=f"Low quality {metric.value} evaluation",
            details={"issues_found": True},
            requires_human_review=score < 0.7
        )
    
    return ComprehensiveEvaluation(
        overall_score=0.5,
        individual_scores=individual_scores,
        requires_human_oversight=True,
        refinement_suggestions=[
            "Improve source verification",
            "Enhance citation quality",
            "Add more comprehensive analysis"
        ],
        metadata={"test_evaluation": True, "quality": "low"}
    )


@pytest.fixture
def mock_llm_response():
    """Create a mock LLM response for testing."""
    mock_response = Mock()
    mock_response.content = """
    Based on Texas law, personal injury claims are subject to a two-year statute of limitations
    under Texas Civil Practice and Remedies Code § 16.003. This means that a lawsuit must be
    filed within two years of when the cause of action accrues.
    
    The cause of action typically accrues when:
    1. The injury occurs
    2. The plaintiff discovers or should have discovered the injury
    3. The plaintiff discovers or should have discovered the cause of the injury
    
    Key considerations include:
    - Discovery rule may extend the limitation period
    - Minority or legal disability may toll the statute
    - Fraudulent concealment may extend the period
    
    [1] Texas Civil Practice and Remedies Code § 16.003
    [2] Smith v. Jones, 123 S.W.3d 456 (Tex. 2020)
    """
    return mock_response


@pytest.fixture
def mock_laws_api_client():
    """Create a mock laws API client for testing."""
    mock_client = AsyncMock()
    
    # Mock search response
    mock_client.search.return_value = {
        "documents": [
            {
                "id": "doc_1",
                "title": "Texas Civil Practice and Remedies Code § 16.003",
                "content": "Personal injury statute of limitations",
                "type": "statute",
                "jurisdiction": "texas"
            }
        ],
        "total": 1,
        "page": 1
    }
    
    # Mock graph expansion response
    mock_client.graph_expand.return_value = {
        "related_documents": [
            {
                "id": "related_1",
                "title": "Related Case Law",
                "content": "Related legal precedent",
                "relationship": "cites"
            }
        ],
        "citation_network": {
            "nodes": ["doc_1", "related_1"],
            "edges": [{"source": "doc_1", "target": "related_1", "type": "cites"}]
        }
    }
    
    return mock_client


@pytest.fixture
def mock_redis_client():
    """Create a mock Redis client for testing."""
    mock_redis = Mock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.lpush.return_value = 1
    mock_redis.lrange.return_value = []
    mock_redis.zadd.return_value = 1
    mock_redis.expire.return_value = True
    return mock_redis


@pytest.fixture
def mock_celery_app():
    """Create a mock Celery app for testing."""
    mock_celery = Mock()
    mock_celery.send_task.return_value = Mock(id="test_task_id")
    mock_celery.control.revoke.return_value = None
    return mock_celery


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test."""
    # Reset any global monitoring state
    from backend.agents.interactive.research.monitoring import research_monitor
    research_monitor.metrics_storage = {}
    research_monitor.audit_trail = []
    
    # Reset UI manager state
    from backend.agents.interactive.research.ui_integration import ui_manager
    ui_manager.notification_queue = {}
    ui_manager.active_sessions = {}
    
    yield
    
    # Cleanup after test if needed


# Test markers for different test categories
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.async_test = pytest.mark.asyncio
pytest.mark.ethical = pytest.mark.ethical
pytest.mark.performance = pytest.mark.performance


# Custom assertions for testing
def assert_evaluation_quality(evaluation: ComprehensiveEvaluation, min_score: float = 0.7):
    """Assert that an evaluation meets minimum quality standards."""
    assert evaluation.overall_score >= min_score, f"Overall score {evaluation.overall_score} below minimum {min_score}"
    
    for metric, result in evaluation.individual_scores.items():
        assert result.score >= 0.0, f"{metric.value} score cannot be negative"
        assert result.score <= 1.0, f"{metric.value} score cannot exceed 1.0"


def assert_state_integrity(state: ResearchState):
    """Assert that a research state maintains integrity."""
    assert state.query_id is not None, "Query ID cannot be None"
    assert state.question is not None and len(state.question) > 0, "Question cannot be empty"
    assert state.jurisdiction is not None, "Jurisdiction cannot be None"
    assert state.user_context is not None, "User context cannot be None"
    assert state.user_context.user_id is not None, "User ID cannot be None"
    assert state.user_context.tenant_id is not None, "Tenant ID cannot be None"


def assert_monitoring_data_complete(monitor, expected_metrics: int = 1):
    """Assert that monitoring data is complete and valid."""
    assert len(monitor.metrics_storage) > 0, "No metrics stored"
    
    total_metrics = sum(len(metrics) for metrics in monitor.metrics_storage.values())
    assert total_metrics >= expected_metrics, f"Expected at least {expected_metrics} metrics, got {total_metrics}"


# Test data constants
TEST_JURISDICTIONS = ["texas", "california", "new_york", "florida"]
TEST_PRACTICE_AREAS = ["personal_injury", "contract_law", "employment_law", "real_estate"]
TEST_QUERY_TYPES = ["research", "analysis", "precedent_search", "statute_lookup"]
