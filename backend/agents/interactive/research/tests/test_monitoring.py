"""
Test suite for the Research Agent Monitoring System

This module contains tests for the monitoring and metrics tracking functionality,
including performance tracking, quality metrics, ethical compliance monitoring,
and audit trail creation.
"""

import pytest
import time
from unittest.mock import Mock, AsyncMock, patch

from backend.agents.interactive.research.state import ResearchState, UserContext
from backend.agents.interactive.research.monitoring import (
    ResearchAgentMonitor,
    PerformanceMetric,
    QualityMetric,
    EthicalComplianceMetric,
    UsageMetric,
    AuditTrailEntry,
    MetricType,
    ComplianceLevel
)
from backend.agents.interactive.research.evaluators import (
    ComprehensiveEvaluation,
    EvaluationResult,
    EvaluationMetric
)
from backend.agents.interactive.research.agentic_wrapper import (
    AgenticExecutionResult,
    AgenticIteration
)


class TestResearchAgentMonitor:
    """Test suite for the ResearchAgentMonitor class."""
    
    @pytest.fixture
    def monitor(self):
        """Create a monitor instance for testing."""
        return ResearchAgentMonitor()
    
    @pytest.fixture
    def sample_state(self):
        """Create a sample research state for testing."""
        return ResearchState(
            query_id="monitor_test_123",
            question="Test monitoring question",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            user_context=UserContext(
                user_id="test_user",
                tenant_id="test_tenant",
                role="attorney"
            ),
            thread_id="test_thread"
        )
    
    @pytest.fixture
    def sample_evaluation(self):
        """Create a sample evaluation for testing."""
        individual_scores = {}
        for metric in EvaluationMetric:
            individual_scores[metric] = EvaluationResult(
                metric=metric,
                score=0.8,
                explanation="Test evaluation",
                details={},
                requires_human_review=False
            )
        
        return ComprehensiveEvaluation(
            overall_score=0.8,
            individual_scores=individual_scores,
            requires_human_oversight=False,
            refinement_suggestions=[],
            metadata={"test": True}
        )
    
    @pytest.fixture
    def sample_execution_result(self):
        """Create a sample execution result for testing."""
        iteration = AgenticIteration(
            iteration_number=1,
            start_time=time.time(),
            end_time=time.time() + 10,
            evaluation_results=None,
            refinement_applied=None,
            success=True
        )
        
        return AgenticExecutionResult(
            final_state=None,
            final_evaluation=None,
            iterations=[iteration],
            total_execution_time=10.0,
            requires_human_oversight=False,
            refinement_history=[]
        )
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, monitor, sample_state, sample_execution_result):
        """Test performance metrics tracking."""
        await monitor.track_performance(
            sample_state,
            sample_execution_result,
            tokens_used=1000,
            api_calls=5,
            cache_hits=3,
            total_requests=5
        )
        
        # Verify metric was stored
        assert MetricType.PERFORMANCE in monitor.metrics_storage
        perf_metrics = monitor.metrics_storage[MetricType.PERFORMANCE]
        assert len(perf_metrics) == 1
        
        metric = perf_metrics[0]
        assert isinstance(metric, PerformanceMetric)
        assert metric.user_id == "test_user"
        assert metric.tenant_id == "test_tenant"
        assert metric.execution_time == 10.0
        assert metric.tokens_used == 1000
        assert metric.api_calls_count == 5
        assert metric.cache_hit_rate == 0.6  # 3/5
        assert metric.success == True
    
    @pytest.mark.asyncio
    async def test_quality_tracking(self, monitor, sample_state, sample_evaluation):
        """Test quality metrics tracking."""
        await monitor.track_quality(sample_state, sample_evaluation, refinement_iterations=2)
        
        # Verify metric was stored
        assert MetricType.QUALITY in monitor.metrics_storage
        quality_metrics = monitor.metrics_storage[MetricType.QUALITY]
        assert len(quality_metrics) == 1
        
        metric = quality_metrics[0]
        assert isinstance(metric, QualityMetric)
        assert metric.overall_score == 0.8
        assert metric.refinement_iterations == 2
        assert metric.human_oversight_required == False
    
    @pytest.mark.asyncio
    async def test_ethical_compliance_tracking(self, monitor, sample_state, sample_evaluation):
        """Test ethical compliance tracking."""
        await monitor.track_ethical_compliance(sample_state, sample_evaluation)
        
        # Verify metric was stored
        assert MetricType.ETHICAL_COMPLIANCE in monitor.metrics_storage
        ethical_metrics = monitor.metrics_storage[MetricType.ETHICAL_COMPLIANCE]
        assert len(ethical_metrics) == 1
        
        metric = ethical_metrics[0]
        assert isinstance(metric, EthicalComplianceMetric)
        assert metric.compliance_level == ComplianceLevel.COMPLIANT
        assert metric.aba_rule_compliance["rule_1_1_competence"] == True
        assert metric.human_supervision_flagged == False
    
    @pytest.mark.asyncio
    async def test_ethical_compliance_violation_detection(self, monitor, sample_state):
        """Test detection of ethical compliance violations."""
        # Create evaluation with low ethical score
        low_ethical_eval = ComprehensiveEvaluation(
            overall_score=0.4,
            individual_scores={
                EvaluationMetric.ETHICAL_COMPLIANCE: EvaluationResult(
                    metric=EvaluationMetric.ETHICAL_COMPLIANCE,
                    score=0.4,  # Low score
                    explanation="Ethical concerns detected",
                    details={},
                    requires_human_review=True
                )
            },
            requires_human_oversight=True,
            refinement_suggestions=["Add disclaimers"],
            metadata={}
        )
        
        await monitor.track_ethical_compliance(sample_state, low_ethical_eval)
        
        ethical_metrics = monitor.metrics_storage[MetricType.ETHICAL_COMPLIANCE]
        metric = ethical_metrics[0]
        
        assert metric.compliance_level == ComplianceLevel.VIOLATION
        assert metric.human_supervision_flagged == True
        assert metric.violation_details is not None
    
    @pytest.mark.asyncio
    async def test_usage_tracking(self, monitor, sample_state):
        """Test usage analytics tracking."""
        await monitor.track_usage(sample_state, processing_mode="async")
        
        # Verify metric was stored
        assert MetricType.USAGE in monitor.metrics_storage
        usage_metrics = monitor.metrics_storage[MetricType.USAGE]
        assert len(usage_metrics) == 1
        
        metric = usage_metrics[0]
        assert isinstance(metric, UsageMetric)
        assert metric.jurisdiction == "texas"
        assert metric.practice_areas == ["personal_injury"]
        assert metric.processing_mode == "async"
        assert metric.query_complexity in ["simple", "medium", "complex"]
    
    @pytest.mark.asyncio
    async def test_audit_trail_creation(self, monitor, sample_state):
        """Test audit trail entry creation."""
        entry_id = await monitor.create_audit_trail(
            sample_state,
            "test_action",
            {"test_data": "value"},
            compliance_impact="high",
            reviewer_required=True
        )
        
        assert entry_id is not None
        assert len(monitor.audit_trail) == 1
        
        entry = monitor.audit_trail[0]
        assert isinstance(entry, AuditTrailEntry)
        assert entry.action == "test_action"
        assert entry.compliance_impact == "high"
        assert entry.reviewer_required == True
        assert entry.retention_period == 2555  # 7 years
    
    @pytest.mark.asyncio
    async def test_performance_summary_generation(self, monitor, sample_state, sample_execution_result):
        """Test performance summary generation."""
        # Add multiple performance metrics
        for i in range(5):
            await monitor.track_performance(
                sample_state,
                sample_execution_result,
                tokens_used=1000 + i * 100,
                api_calls=5 + i,
                cache_hits=3,
                total_requests=5
            )
        
        summary = await monitor.get_performance_summary("test_tenant", hours=24)
        
        assert "total_queries" in summary
        assert summary["total_queries"] == 5
        assert "average_execution_time" in summary
        assert "success_rate" in summary
        assert summary["success_rate"] == 1.0  # All successful
        assert "total_tokens_used" in summary
    
    @pytest.mark.asyncio
    async def test_performance_alert_thresholds(self, monitor, sample_state):
        """Test performance alert threshold checking."""
        # Create execution result with long execution time
        slow_execution = AgenticExecutionResult(
            final_state=None,
            final_evaluation=None,
            iterations=[],
            total_execution_time=400.0,  # Exceeds 300s threshold
            requires_human_oversight=False,
            refinement_history=[]
        )
        
        with patch.object(monitor, '_check_performance_alerts') as mock_alerts:
            await monitor.track_performance(sample_state, slow_execution)
            mock_alerts.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_quality_alert_thresholds(self, monitor, sample_state):
        """Test quality alert threshold checking."""
        # Create low-quality evaluation
        low_quality_eval = ComprehensiveEvaluation(
            overall_score=0.5,  # Below 0.7 threshold
            individual_scores={},
            requires_human_oversight=True,
            refinement_suggestions=["Improve quality"],
            metadata={}
        )
        
        with patch.object(monitor, '_check_quality_alerts') as mock_alerts:
            await monitor.track_quality(sample_state, low_quality_eval, 1)
            mock_alerts.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_metric_storage_cleanup(self, monitor, sample_state, sample_execution_result):
        """Test that metric storage is cleaned up when it gets too large."""
        # Add more than 1000 metrics to trigger cleanup
        for i in range(1005):
            await monitor.track_performance(sample_state, sample_execution_result)
        
        perf_metrics = monitor.metrics_storage[MetricType.PERFORMANCE]
        assert len(perf_metrics) == 1000  # Should be limited to 1000
    
    @pytest.mark.asyncio
    async def test_audit_trail_cleanup(self, monitor, sample_state):
        """Test that audit trail is cleaned up when it gets too large."""
        # Add more than 10000 entries to trigger cleanup
        for i in range(10005):
            await monitor.create_audit_trail(
                sample_state, f"action_{i}", {}, "low", False
            )
        
        assert len(monitor.audit_trail) == 10000  # Should be limited to 10000


class TestMetricDataStructures:
    """Test suite for metric data structures."""
    
    def test_performance_metric_creation(self):
        """Test PerformanceMetric creation and attributes."""
        metric = PerformanceMetric(
            metric_id="test_perf_1",
            timestamp=time.time(),
            user_id="user1",
            tenant_id="tenant1",
            query_id="query1",
            execution_time=15.5,
            iterations_count=2,
            tokens_used=1500,
            api_calls_count=8,
            cache_hit_rate=0.75,
            memory_usage=128.0,
            success=True
        )
        
        assert metric.metric_id == "test_perf_1"
        assert metric.execution_time == 15.5
        assert metric.success == True
        assert metric.error_type is None
    
    def test_quality_metric_creation(self):
        """Test QualityMetric creation and attributes."""
        metric = QualityMetric(
            metric_id="test_qual_1",
            timestamp=time.time(),
            user_id="user1",
            tenant_id="tenant1",
            query_id="query1",
            overall_score=0.85,
            faithfulness_score=0.9,
            hallucination_score=0.8,
            citation_quality_score=0.85,
            legal_accuracy_score=0.9,
            ethical_compliance_score=0.8,
            human_oversight_required=False,
            refinement_iterations=1
        )
        
        assert metric.overall_score == 0.85
        assert metric.human_oversight_required == False
        assert metric.refinement_iterations == 1
    
    def test_ethical_compliance_metric_creation(self):
        """Test EthicalComplianceMetric creation and attributes."""
        aba_compliance = {
            "rule_1_1_competence": True,
            "rule_1_6_confidentiality": True,
            "rule_3_3_candor": True,
            "rule_1_4_communication": True,
            "rule_5_3_supervision": False
        }
        
        metric = EthicalComplianceMetric(
            metric_id="test_eth_1",
            timestamp=time.time(),
            user_id="user1",
            tenant_id="tenant1",
            query_id="query1",
            compliance_level=ComplianceLevel.COMPLIANT,
            aba_rule_compliance=aba_compliance,
            disclaimer_present=True,
            human_supervision_flagged=False,
            confidentiality_protected=True,
            competence_demonstrated=True,
            audit_trail_complete=True
        )
        
        assert metric.compliance_level == ComplianceLevel.COMPLIANT
        assert metric.aba_rule_compliance["rule_1_1_competence"] == True
        assert metric.disclaimer_present == True
    
    def test_audit_trail_entry_creation(self):
        """Test AuditTrailEntry creation and attributes."""
        entry = AuditTrailEntry(
            entry_id="audit_1",
            timestamp=time.time(),
            user_id="user1",
            tenant_id="tenant1",
            query_id="query1",
            action="research_completed",
            details={"score": 0.85, "iterations": 2},
            compliance_impact="medium",
            reviewer_required=True,
            retention_period=2555
        )
        
        assert entry.action == "research_completed"
        assert entry.compliance_impact == "medium"
        assert entry.reviewer_required == True
        assert entry.retention_period == 2555


if __name__ == "__main__":
    # Run tests with: python -m pytest backend/agents/interactive/research/tests/test_monitoring.py -v
    pytest.main([__file__, "-v"])
