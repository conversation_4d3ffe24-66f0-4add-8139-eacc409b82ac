"""
Demonstration Script for Long-Context Hybrid Search Features

This script demonstrates how to use the new long-context processing capabilities
in the Research Agent, including hybrid search and document summarization.
"""

import asyncio
from typing import List

from backend.agents.interactive.research.nodes import (
    configure_search_metadata,
    hybrid_vector_search,
    log_search_metrics
)
from backend.agents.interactive.research.state import ResearchState, Document, UserContext


def create_sample_legal_documents() -> List[Document]:
    """Create sample legal documents for demonstration."""
    return [
        Document(
            page_content="""
            SUPREME COURT OF TEXAS
            
            CASE NO. 19-0123
            
            JOHN DOE, Plaintiff
            v.
            ACME CORPORATION, Defendant
            
            OPINION
            
            This case presents the question of whether a manufacturer can be held liable 
            for injuries caused by a product defect when the defect was not present at 
            the time of manufacture but developed during normal use due to design 
            inadequacies.
            
            FACTUAL BACKGROUND
            
            <PERSON>tiff <PERSON> was injured while operating a piece of industrial 
            equipment manufactured by defendant Acme Corporation. The equipment had 
            been in use for approximately two years when a critical component failed, 
            causing severe injuries to the plaintiff.
            
            Expert testimony established that the component failure was not due to 
            manufacturing defects or improper maintenance, but rather resulted from 
            a design that was inadequate for the stresses of normal operation over 
            time. The design created a condition where metal fatigue would inevitably 
            occur, leading to catastrophic failure.
            
            LEGAL ANALYSIS
            
            Under Texas law, a manufacturer may be held liable for injuries caused by 
            defective products under three theories: manufacturing defect, design defect, 
            and failure to warn. In this case, the plaintiff proceeds under a design 
            defect theory.
            
            To establish a design defect claim, the plaintiff must prove that the 
            product was defectively designed so as to render it unreasonably dangerous 
            as designed, taking into consideration the utility of the product and the 
            risk involved in its use.
            
            The test for design defect in Texas follows the risk-utility analysis 
            established in Turner v. General Motors Corp., 584 S.W.2d 844 (Tex. 1979). 
            Under this analysis, the court must weigh the utility of the product against 
            the risk of harm from its use.
            
            Factors to consider include:
            1. The utility of the product to the user and to the public
            2. The availability of other, safer products to meet the same need
            3. The likelihood of injury and its probable seriousness
            4. The obviousness of the danger
            5. Common knowledge and normal public expectation of the danger
            6. The avoidability of injury by care in use of the product
            7. The ability to eliminate the danger without seriously impairing 
               the usefulness of the product or making it unduly expensive
            
            HOLDING
            
            We hold that a manufacturer may be liable for design defects that manifest 
            over time through normal use, even if the product functioned properly when 
            first manufactured. The key inquiry is whether the design was unreasonably 
            dangerous considering the product's intended use and foreseeable lifespan.
            
            In this case, the evidence supports a finding that Acme Corporation knew 
            or should have known that the component design would lead to failure within 
            the product's expected useful life. The availability of alternative designs 
            that would have prevented the failure, without significantly increasing 
            cost or reducing utility, supports the jury's finding of design defect.
            
            CONCLUSION
            
            The judgment of the court of appeals is AFFIRMED. Acme Corporation is 
            liable for plaintiff's injuries under a design defect theory.
            
            Justice Smith delivered the opinion of the Court, in which Chief Justice 
            Johnson and Justices Brown, Davis, Wilson, Taylor, and Anderson joined.
            """ * 3,  # Make it long enough to trigger summarization
            metadata={
                "id": "doe_v_acme_2023",
                "title": "Doe v. Acme Corporation - Design Defect Liability",
                "jurisdiction": "texas",
                "document_type": "case_law",
                "citation": "Doe v. Acme Corp., 2023 Tex. LEXIS 456",
                "court": "Supreme Court of Texas",
                "date": "2023-03-15",
                "source": "laws_api"
            }
        ),
        Document(
            page_content="""
            Texas Civil Practice and Remedies Code
            Chapter 82 - Products Liability
            
            § 82.001. DEFINITIONS
            
            In this chapter:
            (1) "Claimant" means a party seeking recovery of damages for harm allegedly 
                caused by a defective product, including a plaintiff, counterclaimant, 
                cross-claimant, or third-party plaintiff.
            (2) "Products liability action" means any action against a manufacturer or 
                seller for recovery of damages arising out of personal injury, death, 
                or property damage allegedly caused by a defective product.
            (3) "Seller" means a person who is engaged in the business of distributing 
                or otherwise placing, for any commercial purpose, in the stream of 
                commerce for use or consumption a product or any component part thereof.
            
            § 82.002. PRODUCTS LIABILITY ACTIONS: THEORIES OF LIABILITY
            
            (a) A claimant may recover damages in a products liability action only if 
                the claimant proves that:
                (1) the product was defective when it left the control of the manufacturer 
                    or seller; and
                (2) the defect was a producing cause of the personal injury, property 
                    damage, or death for which damages are sought.
            
            (b) A product is defective if:
                (1) the product contains a manufacturing defect;
                (2) the product contains a design defect; or
                (3) the product contains a marketing defect.
            
            § 82.003. DESIGN DEFECTS
            
            A product contains a design defect if:
            (1) there was a safer alternative design; and
            (2) the defect renders the product unreasonably dangerous.
            
            § 82.004. MARKETING DEFECTS
            
            A product contains a marketing defect if reasonable instructions or warnings 
            regarding foreseeable risks of harm posed by the product were not provided.
            
            § 82.005. STATUTE OF LIMITATIONS
            
            (a) Except as provided by Subsection (b), a products liability action must 
                be commenced not later than two years after the earlier of the date on 
                which:
                (1) the claimant discovered or reasonably should have discovered the 
                    injury and its cause; or
                (2) the claimant discovered or reasonably should have discovered that 
                    the injury was caused by a defective product.
            
            (b) A products liability action for wrongful death must be commenced not 
                later than two years after the death of the injured person.
            
            § 82.006. COMPARATIVE RESPONSIBILITY
            
            In a products liability action, the comparative responsibility of the 
            claimant does not bar recovery by the claimant for damages unless the 
            claimant's percentage of responsibility is greater than 50 percent.
            """,
            metadata={
                "id": "tex_civ_prac_82",
                "title": "Texas Products Liability Act",
                "jurisdiction": "texas",
                "document_type": "statute",
                "citation": "Tex. Civ. Prac. & Rem. Code Ch. 82",
                "effective_date": "1993-09-01",
                "source": "laws_api"
            }
        ),
        Document(
            page_content="""
            COURT OF APPEALS OF TEXAS
            FIRST DISTRICT, HOUSTON
            
            NO. 01-22-00456-CV
            
            MARY JOHNSON, Appellant
            v.
            DEFECTIVE PRODUCTS, INC., Appellee
            
            On Appeal from the 157th District Court
            Harris County, Texas
            Trial Court Cause No. 2021-12345
            
            MEMORANDUM OPINION
            
            This products liability case involves a claim that a household appliance 
            was defectively designed, causing a fire that damaged appellant's home. 
            The trial court granted summary judgment in favor of the manufacturer. 
            We reverse and remand.
            
            BACKGROUND
            
            Mary Johnson purchased a coffee maker manufactured by Defective Products, 
            Inc. in January 2021. In March 2021, while the coffee maker was operating 
            normally, an electrical component overheated and caused a fire that 
            damaged Johnson's kitchen.
            
            Johnson filed suit alleging design defect, manufacturing defect, and 
            failure to warn. The manufacturer moved for summary judgment, arguing 
            that Johnson could not establish the existence of a defect or that any 
            defect was a producing cause of her damages.
            
            ANALYSIS
            
            To survive summary judgment on a design defect claim, Johnson must present 
            evidence raising a genuine issue of material fact as to whether: (1) a 
            safer alternative design existed, and (2) the product was unreasonably 
            dangerous as designed.
            
            Johnson's expert testified that the electrical component that failed was 
            inadequately insulated and that alternative insulation materials were 
            available at the time of manufacture that would have prevented the 
            overheating. The expert further testified that the cost of the safer 
            design would have been minimal.
            
            The manufacturer argues that the component met industry standards and 
            that the failure was an isolated incident. However, Johnson presented 
            evidence of similar failures in other units, suggesting a systemic 
            design problem rather than an isolated manufacturing defect.
            
            CONCLUSION
            
            Viewing the evidence in the light most favorable to Johnson, we conclude 
            that she has raised genuine issues of material fact regarding the 
            existence of a design defect. The trial court's summary judgment is 
            therefore inappropriate.
            
            We REVERSE the trial court's judgment and REMAND for further proceedings.
            """,
            metadata={
                "id": "johnson_v_defective_2022",
                "title": "Johnson v. Defective Products, Inc. - Design Defect Summary Judgment",
                "jurisdiction": "texas",
                "document_type": "case_law",
                "citation": "Johnson v. Defective Prods., Inc., 2022 Tex. App. LEXIS 789",
                "court": "Court of Appeals of Texas, First District",
                "date": "2022-11-08",
                "source": "laws_api"
            }
        )
    ]


async def demonstrate_standard_search():
    """Demonstrate standard hybrid search without long-context processing."""
    print("=== STANDARD HYBRID SEARCH DEMONSTRATION ===\n")
    
    # Create user context
    user_context = UserContext(
        user_id="demo_user",
        tenant_id="demo_tenant",
        role="attorney"
    )
    
    # Create research state
    state = ResearchState(
        question="What are the requirements for proving a design defect in Texas products liability cases?",
        user_context=user_context,
        queries=[
            "Texas design defect requirements",
            "products liability design defect elements",
            "safer alternative design Texas"
        ],
        jurisdiction="texas",
        practice_areas={"products_liability", "personal_injury"}
    )
    
    # Configure for standard processing
    configure_search_metadata(state, chunk_strategy="standard")
    
    print(f"Question: {state.question}")
    print(f"Queries: {state.queries}")
    print(f"Chunk Strategy: {state.search_metadata['chunk_strategy']}")
    print(f"Long-context enabled: {state.search_metadata['long_context_config']['enabled']}")
    print()
    
    # Simulate the search (in real usage, this would call the actual hybrid_vector_search)
    state.legal_documents = create_sample_legal_documents()
    
    # Simulate search metadata
    state.search_metadata.update({
        "hybrid_search": {
            "dense_results": 3,
            "sparse_results": 2,
            "combined_results": 3,
            "final_results": 3,
            "chunk_strategy": "standard"
        }
    })
    
    print("Search completed successfully!")
    print(f"Found {len(state.legal_documents)} documents")
    
    # Log metrics
    log_search_metrics(state)
    print()


async def demonstrate_long_context_search():
    """Demonstrate hybrid search with long-context processing."""
    print("=== LONG-CONTEXT HYBRID SEARCH DEMONSTRATION ===\n")
    
    # Create user context
    user_context = UserContext(
        user_id="demo_user",
        tenant_id="demo_tenant",
        role="attorney"
    )
    
    # Create research state
    state = ResearchState(
        question="Provide a comprehensive analysis of Texas products liability law including recent case developments and statutory requirements.",
        user_context=user_context,
        queries=[
            "Texas products liability comprehensive analysis",
            "design defect manufacturing defect marketing defect",
            "recent Texas products liability cases"
        ],
        jurisdiction="texas",
        practice_areas={"products_liability", "personal_injury"}
    )
    
    # Configure for long-context processing
    configure_search_metadata(
        state,
        chunk_strategy="full_sections",
        long_context_model="gemini-1.5-pro",
        token_threshold=3000
    )
    
    print(f"Question: {state.question}")
    print(f"Queries: {state.queries}")
    print(f"Chunk Strategy: {state.search_metadata['chunk_strategy']}")
    print(f"Long-context enabled: {state.search_metadata['long_context_config']['enabled']}")
    print(f"Token threshold: {state.search_metadata['long_context_config']['token_threshold']}")
    print(f"Long-context model: {state.search_metadata['long_context_config']['model']}")
    print()
    
    # Simulate the search with long documents
    documents = create_sample_legal_documents()
    state.legal_documents = documents
    
    # Simulate summarization results
    for doc in state.legal_documents:
        if len(doc.page_content) > 3000:  # Simulate long document
            doc.metadata["summarized"] = True
            doc.metadata["original_token_count"] = len(doc.page_content) // 4
            doc.metadata["summary_token_count"] = 800
            doc.page_content = f"[SUMMARIZED] {doc.page_content[:200]}... [Key legal principles and holdings preserved]"
    
    # Simulate search metadata with summarization
    state.search_metadata.update({
        "hybrid_search": {
            "dense_results": 3,
            "sparse_results": 2,
            "combined_results": 3,
            "final_results": 3,
            "chunk_strategy": "full_sections"
        },
        "summarization": {
            "documents_summarized": 1,
            "original_tokens": 5000,
            "summary_tokens": 800,
            "compression_ratio": 16.0
        }
    })
    
    print("Long-context search completed successfully!")
    print(f"Found {len(state.legal_documents)} documents")
    print(f"Summarized {state.search_metadata['summarization']['documents_summarized']} documents")
    print(f"Compression ratio: {state.search_metadata['summarization']['compression_ratio']:.1f}%")
    print()
    
    # Show summarized document
    for doc in state.legal_documents:
        if doc.metadata.get("summarized"):
            print(f"Summarized Document: {doc.metadata['title']}")
            print(f"Original tokens: {doc.metadata['original_token_count']}")
            print(f"Summary tokens: {doc.metadata['summary_token_count']}")
            print(f"Content preview: {doc.page_content[:150]}...")
            print()
    
    # Log comprehensive metrics
    log_search_metrics(state)
    print()


async def demonstrate_error_handling():
    """Demonstrate error handling and fallback mechanisms."""
    print("=== ERROR HANDLING DEMONSTRATION ===\n")
    
    user_context = UserContext(
        user_id="demo_user",
        tenant_id="demo_tenant",
        role="attorney"
    )
    
    state = ResearchState(
        question="Test error handling capabilities",
        user_context=user_context,
        queries=["error handling test"],
        jurisdiction="texas"
    )
    
    configure_search_metadata(state, chunk_strategy="full_sections")
    
    print("Simulating various error conditions:")
    print("1. BM25 search failure - system continues with dense results only")
    print("2. Summarization failure - system continues with original documents")
    print("3. Circuit breaker activation - system skips failing components")
    print()
    
    # Simulate error handling results
    state.legal_documents = create_sample_legal_documents()[:1]  # Single document
    
    state.search_metadata.update({
        "hybrid_search": {
            "dense_results": 1,
            "sparse_results": 0,  # BM25 failed
            "combined_results": 1,
            "final_results": 1,
            "chunk_strategy": "full_sections"
        }
    })
    
    print("Error handling completed successfully!")
    print("System maintained functionality despite component failures")
    print(f"Final results: {state.search_metadata['hybrid_search']['final_results']} documents")
    print()


async def main():
    """Main demonstration function."""
    print("LONG-CONTEXT HYBRID SEARCH DEMONSTRATION")
    print("=" * 50)
    print()
    
    await demonstrate_standard_search()
    await demonstrate_long_context_search()
    await demonstrate_error_handling()
    
    print("=== SUMMARY ===")
    print("The long-context hybrid search system provides:")
    print("• Dense vector search via laws-API for semantic similarity")
    print("• Sparse BM25 search for keyword matching")
    print("• Intelligent document summarization for lengthy texts")
    print("• Robust error handling with graceful degradation")
    print("• Comprehensive metrics and monitoring")
    print("• Backward compatibility with existing workflows")
    print()
    print("This enhances the Research Agent's ability to handle complex legal")
    print("queries and lengthy documents while maintaining accuracy and efficiency.")


if __name__ == "__main__":
    asyncio.run(main())
