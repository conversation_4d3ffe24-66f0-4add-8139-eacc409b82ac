"""
AiLex Research Agent Agentic Wrapper

This module implements the agentic wrapper for the Research Agent that adds
self-correction loops, evaluation metrics, and iterative refinement capabilities.

Key Features:
- Runs base research pipeline with evaluation
- Implements self-correction loops for quality improvement
- Supports async processing for long-running tasks
- Integrates with evaluation framework for quality assessment
- Provides human oversight flags for ethical compliance
- Tracks iteration metrics for monitoring and debugging

The wrapper replaces the main entry point in the LangGraph workflow and
orchestrates the entire agentic research process.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.research.state import ResearchState, Document
from backend.agents.interactive.research.evaluators import (
    ResearchEvaluator,
    ComprehensiveEvaluation,
    EvaluationMetric
)
from backend.agents.interactive.research.graph import create_research_graph

# Configure logger
logger = logging.getLogger(__name__)


@dataclass
class AgenticIteration:
    """Represents a single iteration in the agentic loop."""
    iteration_number: int
    start_time: float
    end_time: Optional[float]
    evaluation_results: Optional[ComprehensiveEvaluation]
    refinement_applied: Optional[str]
    success: bool
    error: Optional[str] = None


@dataclass
class AgenticExecutionResult:
    """Result of agentic execution with all iterations."""
    final_state: ResearchState
    final_evaluation: ComprehensiveEvaluation
    iterations: List[AgenticIteration]
    total_execution_time: float
    requires_human_oversight: bool
    refinement_history: List[str]


class AgenticResearchWrapper:
    """
    Agentic wrapper for the Research Agent that implements self-correction loops.
    
    This wrapper orchestrates the research process with evaluation and refinement,
    providing a more autonomous and quality-aware research experience.
    """
    
    def __init__(
        self,
        max_iterations: int = 3,
        min_quality_threshold: float = 0.8,
        evaluator_model: str = "gpt-4o"
    ):
        """
        Initialize the agentic wrapper.
        
        Args:
            max_iterations: Maximum number of refinement iterations
            min_quality_threshold: Minimum quality score to accept
            evaluator_model: Model to use for evaluation
        """
        self.max_iterations = max_iterations
        self.min_quality_threshold = min_quality_threshold
        self.evaluator = ResearchEvaluator(model=evaluator_model)
        self.base_graph = create_research_graph()
        
    async def execute(
        self,
        state: ResearchState,
        config: RunnableConfig
    ) -> AgenticExecutionResult:
        """
        Execute the agentic research process with self-correction loops.
        
        Args:
            state: Initial research state
            config: Runtime configuration
            
        Returns:
            Agentic execution result with all iterations
        """
        start_time = time.time()
        iterations = []
        current_state = state
        
        logger.info(f"Starting agentic research for query: {state.question[:50]}...")
        
        # Initialize metadata for tracking
        if "agentic_metadata" not in current_state.search_metadata:
            current_state.search_metadata["agentic_metadata"] = {
                "start_time": start_time,
                "max_iterations": self.max_iterations,
                "quality_threshold": self.min_quality_threshold
            }
        
        for iteration in range(self.max_iterations):
            iteration_start = time.time()
            logger.info(f"Starting iteration {iteration + 1}/{self.max_iterations}")
            
            # Create iteration record
            current_iteration = AgenticIteration(
                iteration_number=iteration + 1,
                start_time=iteration_start,
                end_time=None,
                evaluation_results=None,
                refinement_applied=None,
                success=False
            )
            
            try:
                # Run the base research pipeline
                pipeline_result = await self._run_base_pipeline(current_state, config)
                
                if not pipeline_result.get("success", False):
                    current_iteration.error = pipeline_result.get("error", "Pipeline execution failed")
                    current_iteration.end_time = time.time()
                    iterations.append(current_iteration)
                    continue
                
                # Extract response and sources for evaluation
                response = current_state.answer or ""
                sources = current_state.legal_documents + current_state.case_documents
                
                # Evaluate the response
                evaluation = await self.evaluator.evaluate_response(
                    current_state, response, sources
                )
                
                current_iteration.evaluation_results = evaluation
                current_iteration.end_time = time.time()
                current_iteration.success = True
                
                # Store evaluation in state metadata
                current_state.search_metadata[f"iteration_{iteration + 1}_evaluation"] = {
                    "overall_score": evaluation.overall_score,
                    "requires_oversight": evaluation.requires_human_oversight,
                    "individual_scores": {
                        metric.value: result.score 
                        for metric, result in evaluation.individual_scores.items()
                    }
                }
                
                logger.info(f"Iteration {iteration + 1} completed. Score: {evaluation.overall_score:.2f}")
                
                # Check if quality threshold is met
                if evaluation.overall_score >= self.min_quality_threshold:
                    logger.info(f"Quality threshold met ({evaluation.overall_score:.2f} >= {self.min_quality_threshold})")
                    iterations.append(current_iteration)
                    break
                
                # Apply refinement if not the last iteration
                if iteration < self.max_iterations - 1:
                    refinement_applied = await self._apply_refinement(
                        current_state, evaluation, iteration + 1
                    )
                    current_iteration.refinement_applied = refinement_applied
                    logger.info(f"Applied refinement: {refinement_applied}")
                
                iterations.append(current_iteration)
                
            except Exception as e:
                logger.error(f"Error in iteration {iteration + 1}: {str(e)}")
                current_iteration.error = str(e)
                current_iteration.end_time = time.time()
                iterations.append(current_iteration)
                
                # Continue to next iteration unless it's the last one
                if iteration == self.max_iterations - 1:
                    break
        
        # Get final evaluation (use last successful iteration)
        final_evaluation = None
        for iteration in reversed(iterations):
            if iteration.evaluation_results:
                final_evaluation = iteration.evaluation_results
                break
        
        # Create fallback evaluation if none succeeded
        if not final_evaluation:
            logger.warning("No successful evaluations, creating fallback")
            final_evaluation = await self._create_fallback_evaluation(current_state)
        
        # Update state with final agentic metadata
        total_time = time.time() - start_time
        current_state.search_metadata["agentic_metadata"].update({
            "end_time": time.time(),
            "total_execution_time": total_time,
            "iterations_completed": len(iterations),
            "final_score": final_evaluation.overall_score,
            "requires_human_oversight": final_evaluation.requires_human_oversight
        })
        
        # Set human oversight flag in state
        if final_evaluation.requires_human_oversight:
            current_state.search_metadata["human_oversight_required"] = True
            logger.warning("Human oversight required for this research result")
        
        logger.info(f"Agentic research completed in {total_time:.2f}s with {len(iterations)} iterations")
        
        return AgenticExecutionResult(
            final_state=current_state,
            final_evaluation=final_evaluation,
            iterations=iterations,
            total_execution_time=total_time,
            requires_human_oversight=final_evaluation.requires_human_oversight,
            refinement_history=[
                iter.refinement_applied for iter in iterations 
                if iter.refinement_applied
            ]
        )
    
    async def _run_base_pipeline(
        self,
        state: ResearchState,
        config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Run the base research pipeline.
        
        Args:
            state: Current research state
            config: Runtime configuration
            
        Returns:
            Pipeline execution result
        """
        try:
            # Execute the base research graph
            result = await self.base_graph.ainvoke(state, config)
            
            # Update state with result
            if isinstance(result, dict):
                for key, value in result.items():
                    if hasattr(state, key):
                        setattr(state, key, value)
            
            return {"success": True, "result": result}
            
        except Exception as e:
            logger.error(f"Base pipeline execution failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _apply_refinement(
        self,
        state: ResearchState,
        evaluation: ComprehensiveEvaluation,
        iteration: int
    ) -> str:
        """
        Apply refinement based on evaluation results.
        
        Args:
            state: Current research state
            evaluation: Evaluation results
            iteration: Current iteration number
            
        Returns:
            Description of refinement applied
        """
        refinements = []
        
        # Apply specific refinements based on evaluation scores
        for metric, result in evaluation.individual_scores.items():
            if result.score < 0.7:  # Below good threshold
                if metric == EvaluationMetric.FAITHFULNESS:
                    # Re-query with more specific terms
                    if state.queries:
                        state.queries = [f"specific details about {q}" for q in state.queries[:3]]
                        refinements.append("Enhanced query specificity for better faithfulness")
                
                elif metric == EvaluationMetric.HALLUCINATION:
                    # Add verification step
                    state.search_metadata[f"iteration_{iteration}_verification"] = True
                    refinements.append("Added verification step to reduce hallucinations")
                
                elif metric == EvaluationMetric.CITATION_QUALITY:
                    # Request more detailed citations
                    state.search_metadata[f"iteration_{iteration}_detailed_citations"] = True
                    refinements.append("Enhanced citation detail requirements")
        
        # Apply general refinements from evaluation suggestions
        if evaluation.refinement_suggestions:
            refinements.extend(evaluation.refinement_suggestions[:2])  # Limit to top 2
        
        return "; ".join(refinements) if refinements else "General quality improvement"

    async def _create_fallback_evaluation(self, state: ResearchState) -> ComprehensiveEvaluation:
        """
        Create a fallback evaluation when all iterations fail.

        Args:
            state: Current research state

        Returns:
            Fallback evaluation with conservative scores
        """
        from backend.agents.interactive.research.evaluators import (
            EvaluationResult, ComprehensiveEvaluation, EvaluationMetric
        )

        # Create conservative evaluation results
        individual_scores = {}
        for metric in EvaluationMetric:
            individual_scores[metric] = EvaluationResult(
                metric=metric,
                score=0.3,  # Conservative score
                explanation="Fallback evaluation due to iteration failures",
                details={"fallback": True},
                requires_human_review=True
            )

        return ComprehensiveEvaluation(
            overall_score=0.3,
            individual_scores=individual_scores,
            requires_human_oversight=True,
            refinement_suggestions=["Manual review required due to processing errors"],
            metadata={"fallback_evaluation": True}
        )


# Main entry point function for LangGraph integration
async def agentic_research_wrapper(
    state: ResearchState,
    config: RunnableConfig
) -> Dict[str, Any]:
    """
    Main agentic wrapper function for LangGraph integration.

    This function serves as the entry point for the agentic research process,
    replacing the original research pipeline with an enhanced version that
    includes evaluation and self-correction capabilities.

    Args:
        state: Research state
        config: Runtime configuration

    Returns:
        Updated state dictionary for LangGraph
    """
    try:
        # Create agentic wrapper instance
        wrapper = AgenticResearchWrapper()

        # Execute agentic research
        result = await wrapper.execute(state, config)

        # Update state with final results
        final_state = result.final_state

        # Add agentic execution metadata
        final_state.search_metadata["agentic_execution"] = {
            "iterations_completed": len(result.iterations),
            "total_execution_time": result.total_execution_time,
            "final_score": result.final_evaluation.overall_score,
            "requires_human_oversight": result.requires_human_oversight,
            "refinement_history": result.refinement_history
        }

        # Set confidence flags based on evaluation
        if result.final_evaluation.overall_score < 0.6:
            final_state.low_confidence_rerank = True

        if result.requires_human_oversight:
            final_state.search_metadata["human_oversight_required"] = True

        logger.info(f"Agentic wrapper completed successfully. Final score: {result.final_evaluation.overall_score:.2f}")

        return {"status": "success", "next": "FINISH"}

    except Exception as e:
        logger.error(f"Agentic wrapper failed: {str(e)}")

        # Add error information to state
        state.search_metadata["agentic_error"] = {
            "error": str(e),
            "fallback_used": True
        }

        # Set human oversight required due to error
        state.search_metadata["human_oversight_required"] = True

        return {"status": "error", "error": str(e), "next": "FINISH"}
