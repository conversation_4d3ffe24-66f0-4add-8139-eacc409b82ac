"""
AiLex Research Agent Async Task Manager

This module extends the Celery infrastructure for long-running research tasks
with Redis state persistence, progress tracking, and real-time updates.

Key Features:
- Async task orchestration for overnight research processing
- Redis-based state persistence and recovery
- Progress tracking with real-time updates
- Task queuing and priority management
- Error handling and retry mechanisms
- Integration with CopilotKit for UI notifications
- Comprehensive logging and monitoring
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

import redis
from celery import Celery
from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.research.state import ResearchState

# Mock Celery config for now


def get_celery_config():
    return {
        'broker_url': 'redis://localhost:6379/0',
        'result_backend': 'redis://localhost:6379/0',
        'task_serializer': 'json',
        'accept_content': ['json'],
        'result_serializer': 'json',
        'timezone': 'UTC',
        'enable_utc': True,
    }


# Configure logger
logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class AsyncResearchTask:
    """Represents an async research task."""
    task_id: str
    user_id: str
    tenant_id: str
    thread_id: str
    matter_id: Optional[str]
    question: str
    jurisdiction: str
    practice_areas: List[str]
    priority: TaskPriority
    status: TaskStatus
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    progress: float = 0.0
    current_step: str = "queued"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    iterations_completed: int = 0
    estimated_completion: Optional[float] = None


@dataclass
class TaskProgress:
    """Progress update for a task."""
    task_id: str
    progress: float
    current_step: str
    message: str
    timestamp: float
    iteration: Optional[int] = None
    evaluation_score: Optional[float] = None


class AsyncResearchManager:
    """
    Manager for async research tasks with Redis persistence and Celery integration.

    Handles long-running research tasks that can be processed overnight or during
    periods of low system usage, with comprehensive progress tracking and recovery.
    """

    def __init__(self):
        """Initialize the async research manager."""
        self.redis_client = redis.from_url(
            "redis://localhost:6379/0",  # TODO: Get from config
            decode_responses=True
        )
        self.celery_app = Celery('research_tasks')
        self.celery_app.conf.update(get_celery_config())

        # Task configuration
        self.task_timeout = 3600  # 1 hour timeout
        self.max_retries = 3
        self.retry_delay = 300  # 5 minutes

        # Redis keys
        self.task_key_prefix = "research_task:"
        self.progress_key_prefix = "research_progress:"
        self.queue_key = "research_queue"

    async def submit_async_research(
        self,
        state: ResearchState,
        config: RunnableConfig,
        priority: TaskPriority = TaskPriority.NORMAL
    ) -> str:
        """
        Submit a research task for async processing.

        Args:
            state: Research state
            config: Runtime configuration
            priority: Task priority

        Returns:
            Task ID for tracking
        """
        task_id = str(uuid.uuid4())

        # Create task record
        task = AsyncResearchTask(
            task_id=task_id,
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            thread_id=state.thread_id or str(uuid.uuid4()),
            matter_id=getattr(state, 'matter_id', None),
            question=state.question,
            jurisdiction=state.jurisdiction,
            practice_areas=list(state.practice_areas),
            priority=priority,
            status=TaskStatus.PENDING,
            created_at=time.time(),
            estimated_completion=time.time() + 1800  # 30 minutes estimate
        )

        # Store task in Redis
        await self._store_task(task)

        # Add to priority queue
        await self._enqueue_task(task_id, priority)

        # Submit to Celery
        self.celery_app.send_task(
            'research_tasks.process_async_research',
            args=[task_id, state.model_dump(), asdict(config) if config else {}],
            task_id=task_id,
            priority=priority.value,
            expires=self.task_timeout
        )

        logger.info(
            f"Submitted async research task {task_id} for user {state.user_context.user_id}")

        return task_id

    async def get_task_status(self, task_id: str) -> Optional[AsyncResearchTask]:
        """
        Get current status of a research task.

        Args:
            task_id: Task identifier

        Returns:
            Task status or None if not found
        """
        task_data = self.redis_client.get(f"{self.task_key_prefix}{task_id}")
        if task_data:
            return AsyncResearchTask(**json.loads(task_data))
        return None

    async def get_task_progress(self, task_id: str) -> List[TaskProgress]:
        """
        Get progress updates for a task.

        Args:
            task_id: Task identifier

        Returns:
            List of progress updates
        """
        progress_key = f"{self.progress_key_prefix}{task_id}"
        progress_data = self.redis_client.lrange(progress_key, 0, -1)

        progress_updates = []
        for data in progress_data:
            progress_updates.append(TaskProgress(**json.loads(data)))

        return progress_updates

    async def cancel_task(self, task_id: str, user_id: str) -> bool:
        """
        Cancel a pending or running task.

        Args:
            task_id: Task identifier
            user_id: User requesting cancellation

        Returns:
            True if cancelled successfully
        """
        task = await self.get_task_status(task_id)
        if not task or task.user_id != user_id:
            return False

        if task.status in [
                TaskStatus.COMPLETED,
                TaskStatus.FAILED,
                TaskStatus.CANCELLED]:
            return False

        # Update task status
        task.status = TaskStatus.CANCELLED
        await self._store_task(task)

        # Revoke Celery task
        self.celery_app.control.revoke(task_id, terminate=True)

        logger.info(f"Cancelled research task {task_id}")
        return True

    async def update_progress(
        self,
        task_id: str,
        progress: float,
        current_step: str,
        message: str,
        iteration: Optional[int] = None,
        evaluation_score: Optional[float] = None
    ) -> None:
        """
        Update task progress.

        Args:
            task_id: Task identifier
            progress: Progress percentage (0.0 to 1.0)
            current_step: Current processing step
            message: Progress message
            iteration: Current iteration number
            evaluation_score: Current evaluation score
        """
        # Update task record
        task = await self.get_task_status(task_id)
        if task:
            task.progress = progress
            task.current_step = current_step
            if iteration is not None:
                task.iterations_completed = iteration
            await self._store_task(task)

        # Add progress update
        progress_update = TaskProgress(
            task_id=task_id,
            progress=progress,
            current_step=current_step,
            message=message,
            timestamp=time.time(),
            iteration=iteration,
            evaluation_score=evaluation_score
        )

        progress_key = f"{self.progress_key_prefix}{task_id}"
        self.redis_client.lpush(progress_key, json.dumps(asdict(progress_update)))
        self.redis_client.expire(progress_key, 86400)  # 24 hours

        # TODO: Send real-time update via CopilotKit/WebSocket
        await self._send_realtime_update(task_id, progress_update)

        logger.debug(f"Updated progress for task {task_id}: {progress:.1%} - {message}")

    async def complete_task(
        self,
        task_id: str,
        result: Dict[str, Any],
        success: bool = True,
        error: Optional[str] = None
    ) -> None:
        """
        Mark task as completed.

        Args:
            task_id: Task identifier
            result: Task result data
            success: Whether task completed successfully
            error: Error message if failed
        """
        task = await self.get_task_status(task_id)
        if not task:
            return

        task.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        task.completed_at = time.time()
        task.result = result
        task.error = error
        task.progress = 1.0

        await self._store_task(task)

        # Send completion notification
        await self._send_completion_notification(task)

        logger.info(f"Task {task_id} completed with status: {task.status.value}")

    async def _store_task(self, task: AsyncResearchTask) -> None:
        """Store task in Redis."""
        task_key = f"{self.task_key_prefix}{task.task_id}"
        self.redis_client.set(task_key, json.dumps(asdict(task)), ex=86400)  # 24 hours

    async def _enqueue_task(self, task_id: str, priority: TaskPriority) -> None:
        """Add task to priority queue."""
        score = priority.value * 1000 + time.time()  # Priority + timestamp
        self.redis_client.zadd(self.queue_key, {task_id: score})

    async def _send_realtime_update(self, task_id: str, progress: TaskProgress) -> None:
        """Send real-time progress update via CopilotKit."""
        try:
            from backend.agents.interactive.research.ui_integration import ui_manager

            # Get task to extract user info
            task = await self.get_task_status(task_id)
            if task:
                await ui_manager.send_progress_notification(
                    task.user_id, task.thread_id, progress
                )
        except Exception as e:
            logger.error(f"Failed to send real-time update: {str(e)}")

    async def _send_completion_notification(self, task: AsyncResearchTask) -> None:
        """Send task completion notification."""
        try:
            from backend.agents.interactive.research.ui_integration import ui_manager
            await ui_manager.send_task_completion(task)
        except Exception as e:
            logger.error(f"Failed to send completion notification: {str(e)}")


# Celery task definition
def process_async_research(
        task_id: str, state_data: Dict[str, Any], config_data: Dict[str, Any]):
    """
    Celery task for processing async research.

    Args:
        task_id: Task identifier
        state_data: Serialized research state
        config_data: Serialized runtime config
    """
    async def _process():
        manager = AsyncResearchManager()

        try:
            # Update task status
            await manager.update_progress(
                task_id, 0.1, "initializing", "Starting research processing..."
            )

            # Reconstruct state and config
            state = ResearchState.from_dict(state_data)
            config = RunnableConfig(**config_data) if config_data else None

            # Import agentic wrapper here to avoid circular imports
            from backend.agents.interactive.research.agentic_wrapper import AgenticResearchWrapper
            wrapper = AgenticResearchWrapper()

            # Execute research with progress tracking
            await manager.update_progress(
                task_id, 0.2, "researching", "Running agentic research pipeline..."
            )

            result = await wrapper.execute(state, config)

            # Update progress during iterations
            for i, iteration in enumerate(result.iterations):
                progress = 0.2 + (0.7 * (i + 1) / len(result.iterations))
                await manager.update_progress(
                    task_id, progress, f"iteration_{i+1}",
                    f"Completed iteration {i+1}/{len(result.iterations)}",
                    iteration=i + 1,
                    evaluation_score=iteration.evaluation_results.overall_score if iteration.evaluation_results else None
                )

            # Complete task
            await manager.complete_task(
                task_id,
                {
                    "final_state": result.final_state.model_dump(),
                    "evaluation": asdict(result.final_evaluation),
                    "iterations": len(result.iterations),
                    "execution_time": result.total_execution_time,
                    "requires_oversight": result.requires_human_oversight
                },
                success=True
            )

        except Exception as e:
            logger.error(f"Async research task {task_id} failed: {str(e)}")
            await manager.complete_task(task_id, {}, success=False, error=str(e))

    # Run async function
    asyncio.run(_process())
