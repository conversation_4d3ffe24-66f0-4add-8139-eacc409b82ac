"""
Research Agent Package

This package contains the Research Agent implementation for the AiLex system.
The Research Agent handles quick research queries and queues longer research tasks.

Key Features:
- Supports AI-powered legal research for solo and small-firm lawyers.
- Automatically classifies user queries by type and data source needs.
- Supports both legal database search and web search paths.
- Provides citation tracking and formatting for search results.
- Integrates with CopilotKit Cloud for seamless frontend-backend communication.

The agent uses a hybrid retrieval pipeline with:
- Query generation using LLMs
- Legal document search via laws-API service
- Graph expansion using laws-API knowledge graph
- Document recommendations via laws-API
- Reranking with Voyage via Groq
- Web search as an alternative path
"""

from backend.agents.interactive.research.agent import ResearchAgent
from backend.agents.interactive.research.state import (
    Citation,
    ResearchState,
    SearchResult,
)

__all__ = [
    "ResearchAgent",
    "ResearchState",
    "Citation",
    "SearchResult",
]
