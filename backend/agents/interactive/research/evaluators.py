"""
AiLex Research Agent Evaluation Framework

This module implements comprehensive evaluation metrics for the Research Agent,
including faithfulness scoring, hallucination detection, and confidence assessment
with legal-specific criteria for ethical compliance and accuracy.

Key Features:
- Faithfulness evaluation: Compare answers to source documents
- Hallucination detection: Identify unsupported claims
- Citation quality assessment: Verify citation accuracy and relevance
- Legal-specific metrics: Evaluate compliance with legal research standards
- Confidence scoring: Aggregate multiple metrics for overall confidence
- Ethical compliance: Flag content requiring human oversight
"""

import asyncio
import json
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# LangChain evaluation imports (optional, will be used when available)
try:
    from langchain.evaluation import load_evaluator
    LANGCHAIN_EVAL_AVAILABLE = True
except ImportError:
    LANGCHAIN_EVAL_AVAILABLE = False

from backend.agents.interactive.research.state import ResearchState, Document
from shared.core.llm import LLMConfig, LLMMessage, generate_completion

# Configure logger
logger = logging.getLogger(__name__)


class EvaluationMetric(Enum):
    """Enumeration of available evaluation metrics."""
    FAITHFULNESS = "faithfulness"
    HALLUCINATION = "hallucination"
    CITATION_QUALITY = "citation_quality"
    LEGAL_ACCURACY = "legal_accuracy"
    CONFIDENCE = "confidence"
    ETHICAL_COMPLIANCE = "ethical_compliance"


@dataclass
class EvaluationResult:
    """Result of an evaluation metric."""
    metric: EvaluationMetric
    score: float  # 0.0 to 1.0
    explanation: str
    details: Dict[str, Any]
    requires_human_review: bool = False


@dataclass
class ComprehensiveEvaluation:
    """Comprehensive evaluation results for a research response."""
    overall_score: float
    individual_scores: Dict[EvaluationMetric, EvaluationResult]
    requires_human_oversight: bool
    refinement_suggestions: List[str]
    metadata: Dict[str, Any]


class ResearchEvaluator:
    """
    Comprehensive evaluator for Research Agent responses.
    
    Implements multiple evaluation metrics to assess the quality, accuracy,
    and ethical compliance of legal research responses.
    """
    
    def __init__(self, model: str = "gpt-4o"):
        """
        Initialize the evaluator.
        
        Args:
            model: LLM model to use for evaluation
        """
        self.model = model
        self.faithfulness_threshold = 0.8
        self.hallucination_threshold = 0.2
        self.citation_quality_threshold = 0.7
        self.overall_confidence_threshold = 0.9
        
    async def evaluate_response(
        self,
        state: ResearchState,
        response: str,
        sources: List[Document]
    ) -> ComprehensiveEvaluation:
        """
        Perform comprehensive evaluation of a research response.
        
        Args:
            state: Research state with context
            response: Generated response text
            sources: Source documents used
            
        Returns:
            Comprehensive evaluation results
        """
        logger.info(f"Starting comprehensive evaluation for query: {state.question[:50]}...")
        
        # Run all evaluations in parallel
        evaluation_tasks = [
            self._evaluate_faithfulness(response, sources),
            self._evaluate_hallucination(response, sources),
            self._evaluate_citation_quality(response, state.citations),
            self._evaluate_legal_accuracy(response, state),
            self._evaluate_ethical_compliance(response, state)
        ]
        
        results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        individual_scores = {}
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Evaluation {i} failed: {result}")
                # Create fallback result
                metric = list(EvaluationMetric)[i]
                individual_scores[metric] = EvaluationResult(
                    metric=metric,
                    score=0.5,  # Neutral score on failure
                    explanation=f"Evaluation failed: {str(result)}",
                    details={"error": str(result)},
                    requires_human_review=True
                )
            else:
                individual_scores[result.metric] = result
        
        # Calculate overall score and determine oversight requirements
        overall_score = self._calculate_overall_score(individual_scores)
        requires_oversight = self._requires_human_oversight(individual_scores, overall_score)
        refinement_suggestions = self._generate_refinement_suggestions(individual_scores)
        
        logger.info(f"Evaluation complete. Overall score: {overall_score:.2f}, Oversight required: {requires_oversight}")
        
        return ComprehensiveEvaluation(
            overall_score=overall_score,
            individual_scores=individual_scores,
            requires_human_oversight=requires_oversight,
            refinement_suggestions=refinement_suggestions,
            metadata={
                "evaluation_timestamp": asyncio.get_event_loop().time(),
                "model_used": self.model,
                "query_id": state.query_id,
                "jurisdiction": state.jurisdiction
            }
        )
    
    async def _evaluate_faithfulness(self, response: str, sources: List[Document]) -> EvaluationResult:
        """
        Evaluate faithfulness of response to source documents.
        
        Args:
            response: Generated response
            sources: Source documents
            
        Returns:
            Faithfulness evaluation result
        """
        if not sources:
            return EvaluationResult(
                metric=EvaluationMetric.FAITHFULNESS,
                score=0.0,
                explanation="No sources available for faithfulness evaluation",
                details={"source_count": 0},
                requires_human_review=True
            )
        
        # Combine source content for evaluation
        source_content = "\n\n".join([
            f"Source {i+1}: {doc.page_content[:500]}..."
            for i, doc in enumerate(sources[:5])  # Limit to first 5 sources
        ])
        
        faithfulness_prompt = f"""
        Evaluate how well the following response is supported by the provided sources.
        
        Response to evaluate:
        {response}
        
        Source documents:
        {source_content}
        
        Rate the faithfulness on a scale of 0.0 to 1.0 where:
        - 1.0: Response is fully supported by sources with accurate citations
        - 0.8: Response is mostly supported with minor unsupported details
        - 0.6: Response is partially supported but contains some unsupported claims
        - 0.4: Response contains significant unsupported content
        - 0.2: Response is mostly unsupported by sources
        - 0.0: Response contradicts or is completely unsupported by sources
        
        Provide your evaluation as JSON:
        {{
            "score": 0.0-1.0,
            "explanation": "Detailed explanation of faithfulness assessment",
            "supported_claims": ["list of well-supported claims"],
            "unsupported_claims": ["list of unsupported or questionable claims"],
            "citation_accuracy": "assessment of citation accuracy"
        }}
        """
        
        messages = [
            LLMMessage(role="system", content="You are an expert evaluator specializing in assessing the faithfulness of responses to source documents. Focus on accuracy and proper attribution."),
            LLMMessage(role="user", content=faithfulness_prompt)
        ]
        
        try:
            response_obj = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )
            
            # Parse JSON response
            result_text = response_obj.content.strip()
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            
            if json_match:
                result_data = json.loads(json_match.group())
                score = float(result_data.get("score", 0.5))
                explanation = result_data.get("explanation", "Faithfulness evaluation completed")
                
                return EvaluationResult(
                    metric=EvaluationMetric.FAITHFULNESS,
                    score=score,
                    explanation=explanation,
                    details={
                        "supported_claims": result_data.get("supported_claims", []),
                        "unsupported_claims": result_data.get("unsupported_claims", []),
                        "citation_accuracy": result_data.get("citation_accuracy", ""),
                        "source_count": len(sources)
                    },
                    requires_human_review=score < self.faithfulness_threshold
                )
            else:
                raise ValueError("Could not parse JSON response")
                
        except Exception as e:
            logger.error(f"Faithfulness evaluation failed: {e}")
            return EvaluationResult(
                metric=EvaluationMetric.FAITHFULNESS,
                score=0.5,
                explanation=f"Evaluation failed: {str(e)}",
                details={"error": str(e), "source_count": len(sources)},
                requires_human_review=True
            )

    async def _evaluate_hallucination(self, response: str, sources: List[Document]) -> EvaluationResult:
        """
        Detect potential hallucinations in the response.

        Args:
            response: Generated response
            sources: Source documents

        Returns:
            Hallucination evaluation result
        """
        source_content = "\n\n".join([
            f"Source {i+1}: {doc.page_content[:500]}..."
            for i, doc in enumerate(sources[:5])
        ]) if sources else "No sources provided"

        hallucination_prompt = f"""
        Analyze the following response for potential hallucinations - claims that are not supported by the provided sources.

        Response to analyze:
        {response}

        Available sources:
        {source_content}

        Look for:
        1. Factual claims not present in sources
        2. Misrepresented information from sources
        3. Invented case names, statutes, or legal precedents
        4. Unsupported legal conclusions or interpretations

        Rate hallucination risk on a scale of 0.0 to 1.0 where:
        - 0.0: No hallucinations detected, all claims supported
        - 0.2: Minor unsupported details that don't affect core message
        - 0.4: Some unsupported claims but mostly accurate
        - 0.6: Significant unsupported content
        - 0.8: Major hallucinations affecting reliability
        - 1.0: Severe hallucinations, response unreliable

        Provide your evaluation as JSON:
        {{
            "score": 0.0-1.0,
            "explanation": "Detailed explanation of hallucination assessment",
            "potential_hallucinations": ["list of potential hallucinated content"],
            "confidence_level": "high/medium/low",
            "recommendation": "accept/review/reject"
        }}
        """

        messages = [
            LLMMessage(role="system", content="You are an expert at detecting hallucinations in legal research responses. Be thorough but fair in your assessment."),
            LLMMessage(role="user", content=hallucination_prompt)
        ]

        try:
            response_obj = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            result_text = response_obj.content.strip()
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)

            if json_match:
                result_data = json.loads(json_match.group())
                score = float(result_data.get("score", 0.5))
                explanation = result_data.get("explanation", "Hallucination evaluation completed")

                return EvaluationResult(
                    metric=EvaluationMetric.HALLUCINATION,
                    score=1.0 - score,  # Invert score (lower hallucination = higher quality)
                    explanation=explanation,
                    details={
                        "hallucination_risk": score,
                        "potential_hallucinations": result_data.get("potential_hallucinations", []),
                        "confidence_level": result_data.get("confidence_level", "medium"),
                        "recommendation": result_data.get("recommendation", "review")
                    },
                    requires_human_review=score > self.hallucination_threshold
                )
            else:
                raise ValueError("Could not parse JSON response")

        except Exception as e:
            logger.error(f"Hallucination evaluation failed: {e}")
            return EvaluationResult(
                metric=EvaluationMetric.HALLUCINATION,
                score=0.5,
                explanation=f"Evaluation failed: {str(e)}",
                details={"error": str(e)},
                requires_human_review=True
            )

    async def _evaluate_citation_quality(self, response: str, citations: List[Dict[str, Any]]) -> EvaluationResult:
        """
        Evaluate the quality and accuracy of citations.

        Args:
            response: Generated response
            citations: List of citations

        Returns:
            Citation quality evaluation result
        """
        if not citations:
            return EvaluationResult(
                metric=EvaluationMetric.CITATION_QUALITY,
                score=0.0,
                explanation="No citations provided",
                details={"citation_count": 0},
                requires_human_review=True
            )

        # Count citation references in response
        citation_refs = re.findall(r'\[(\d+)\]', response)
        unique_refs = set(citation_refs)

        citation_analysis_prompt = f"""
        Evaluate the quality of citations in this legal research response.

        Response:
        {response}

        Citations provided:
        {json.dumps(citations, indent=2)}

        Citation references found in text: {list(unique_refs)}

        Evaluate based on:
        1. Citation completeness (all necessary information present)
        2. Citation accuracy (proper legal citation format)
        3. Citation relevance (citations support the claims made)
        4. Citation coverage (all claims are properly cited)

        Rate citation quality on a scale of 0.0 to 1.0 where:
        - 1.0: Excellent citations, complete and accurate
        - 0.8: Good citations with minor formatting issues
        - 0.6: Adequate citations but missing some details
        - 0.4: Poor citations with significant issues
        - 0.2: Very poor citations, mostly inadequate
        - 0.0: No usable citations or completely inaccurate

        Provide your evaluation as JSON:
        {{
            "score": 0.0-1.0,
            "explanation": "Detailed explanation of citation quality",
            "citation_coverage": "percentage of claims properly cited",
            "format_accuracy": "assessment of citation format",
            "missing_citations": ["claims that need citations"],
            "citation_issues": ["specific issues found"]
        }}
        """

        messages = [
            LLMMessage(role="system", content="You are an expert in legal citation standards and research quality assessment."),
            LLMMessage(role="user", content=citation_analysis_prompt)
        ]

        try:
            response_obj = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            result_text = response_obj.content.strip()
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)

            if json_match:
                result_data = json.loads(json_match.group())
                score = float(result_data.get("score", 0.5))
                explanation = result_data.get("explanation", "Citation quality evaluation completed")

                return EvaluationResult(
                    metric=EvaluationMetric.CITATION_QUALITY,
                    score=score,
                    explanation=explanation,
                    details={
                        "citation_count": len(citations),
                        "references_in_text": len(unique_refs),
                        "citation_coverage": result_data.get("citation_coverage", "unknown"),
                        "format_accuracy": result_data.get("format_accuracy", "unknown"),
                        "missing_citations": result_data.get("missing_citations", []),
                        "citation_issues": result_data.get("citation_issues", [])
                    },
                    requires_human_review=score < self.citation_quality_threshold
                )
            else:
                raise ValueError("Could not parse JSON response")

        except Exception as e:
            logger.error(f"Citation quality evaluation failed: {e}")
            return EvaluationResult(
                metric=EvaluationMetric.CITATION_QUALITY,
                score=0.5,
                explanation=f"Evaluation failed: {str(e)}",
                details={"error": str(e), "citation_count": len(citations)},
                requires_human_review=True
            )

    async def _evaluate_legal_accuracy(self, response: str, state: ResearchState) -> EvaluationResult:
        """
        Evaluate legal accuracy and compliance with legal research standards.

        Args:
            response: Generated response
            state: Research state with context

        Returns:
            Legal accuracy evaluation result
        """
        legal_accuracy_prompt = f"""
        Evaluate the legal accuracy of this research response for {state.jurisdiction} jurisdiction.

        Response:
        {response}

        Query context: {state.question}
        Jurisdiction: {state.jurisdiction}
        Practice areas: {list(state.practice_areas)}

        Evaluate based on:
        1. Legal reasoning accuracy
        2. Proper application of jurisdiction-specific law
        3. Appropriate legal terminology usage
        4. Logical legal conclusions
        5. Awareness of legal limitations and disclaimers

        Rate legal accuracy on a scale of 0.0 to 1.0 where:
        - 1.0: Legally sound with accurate reasoning
        - 0.8: Mostly accurate with minor issues
        - 0.6: Generally accurate but some concerns
        - 0.4: Significant legal accuracy issues
        - 0.2: Major legal errors or misconceptions
        - 0.0: Legally inaccurate or misleading

        Provide your evaluation as JSON:
        {{
            "score": 0.0-1.0,
            "explanation": "Detailed explanation of legal accuracy assessment",
            "legal_strengths": ["aspects that are legally sound"],
            "legal_concerns": ["potential legal accuracy issues"],
            "jurisdiction_compliance": "assessment of jurisdiction-specific accuracy",
            "disclaimer_adequacy": "assessment of legal disclaimers"
        }}
        """

        messages = [
            LLMMessage(role="system", content="You are an expert legal researcher and accuracy assessor with deep knowledge of legal standards and jurisdictional requirements."),
            LLMMessage(role="user", content=legal_accuracy_prompt)
        ]

        try:
            response_obj = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            result_text = response_obj.content.strip()
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)

            if json_match:
                result_data = json.loads(json_match.group())
                score = float(result_data.get("score", 0.5))
                explanation = result_data.get("explanation", "Legal accuracy evaluation completed")

                return EvaluationResult(
                    metric=EvaluationMetric.LEGAL_ACCURACY,
                    score=score,
                    explanation=explanation,
                    details={
                        "legal_strengths": result_data.get("legal_strengths", []),
                        "legal_concerns": result_data.get("legal_concerns", []),
                        "jurisdiction_compliance": result_data.get("jurisdiction_compliance", "unknown"),
                        "disclaimer_adequacy": result_data.get("disclaimer_adequacy", "unknown"),
                        "jurisdiction": state.jurisdiction,
                        "practice_areas": list(state.practice_areas)
                    },
                    requires_human_review=score < 0.7  # Higher threshold for legal accuracy
                )
            else:
                raise ValueError("Could not parse JSON response")

        except Exception as e:
            logger.error(f"Legal accuracy evaluation failed: {e}")
            return EvaluationResult(
                metric=EvaluationMetric.LEGAL_ACCURACY,
                score=0.5,
                explanation=f"Evaluation failed: {str(e)}",
                details={"error": str(e), "jurisdiction": state.jurisdiction},
                requires_human_review=True
            )

    async def _evaluate_ethical_compliance(self, response: str, state: ResearchState) -> EvaluationResult:
        """
        Evaluate ethical compliance with ABA guidelines for AI in law.

        Args:
            response: Generated response
            state: Research state with context

        Returns:
            Ethical compliance evaluation result
        """
        ethical_prompt = f"""
        Evaluate this legal research response for compliance with ABA ethical guidelines for AI in law.

        Response:
        {response}

        Evaluate based on ABA Model Rules and AI guidelines:
        1. Competence (Rule 1.1): Is the response competent and thorough?
        2. Confidentiality (Rule 1.6): Does it protect client confidentiality?
        3. Candor (Rule 3.3): Is it honest about limitations and uncertainties?
        4. Communication (Rule 1.4): Is it clear about AI assistance and limitations?
        5. Supervision (Rule 5.3): Does it indicate need for attorney review?

        Rate ethical compliance on a scale of 0.0 to 1.0 where:
        - 1.0: Fully compliant with ethical guidelines
        - 0.8: Mostly compliant with minor concerns
        - 0.6: Generally compliant but some issues
        - 0.4: Significant ethical concerns
        - 0.2: Major ethical violations
        - 0.0: Serious ethical problems

        Provide your evaluation as JSON:
        {{
            "score": 0.0-1.0,
            "explanation": "Detailed explanation of ethical compliance",
            "compliance_strengths": ["ethical aspects handled well"],
            "ethical_concerns": ["potential ethical issues"],
            "requires_attorney_review": true/false,
            "disclaimer_adequacy": "assessment of ethical disclaimers"
        }}
        """

        messages = [
            LLMMessage(role="system", content="You are an expert in legal ethics and ABA guidelines for AI use in legal practice."),
            LLMMessage(role="user", content=ethical_prompt)
        ]

        try:
            response_obj = generate_completion(
                messages=messages,
                config=LLMConfig(model=self.model, temperature=0.1)
            )

            result_text = response_obj.content.strip()
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)

            if json_match:
                result_data = json.loads(json_match.group())
                score = float(result_data.get("score", 0.5))
                explanation = result_data.get("explanation", "Ethical compliance evaluation completed")

                return EvaluationResult(
                    metric=EvaluationMetric.ETHICAL_COMPLIANCE,
                    score=score,
                    explanation=explanation,
                    details={
                        "compliance_strengths": result_data.get("compliance_strengths", []),
                        "ethical_concerns": result_data.get("ethical_concerns", []),
                        "requires_attorney_review": result_data.get("requires_attorney_review", True),
                        "disclaimer_adequacy": result_data.get("disclaimer_adequacy", "unknown")
                    },
                    requires_human_review=score < 0.8 or result_data.get("requires_attorney_review", True)
                )
            else:
                raise ValueError("Could not parse JSON response")

        except Exception as e:
            logger.error(f"Ethical compliance evaluation failed: {e}")
            return EvaluationResult(
                metric=EvaluationMetric.ETHICAL_COMPLIANCE,
                score=0.5,
                explanation=f"Evaluation failed: {str(e)}",
                details={"error": str(e)},
                requires_human_review=True
            )

    def _calculate_overall_score(self, individual_scores: Dict[EvaluationMetric, EvaluationResult]) -> float:
        """
        Calculate overall score from individual evaluation results.

        Args:
            individual_scores: Dictionary of individual evaluation results

        Returns:
            Overall score (0.0 to 1.0)
        """
        if not individual_scores:
            return 0.0

        # Weighted scoring - some metrics are more important
        weights = {
            EvaluationMetric.FAITHFULNESS: 0.25,
            EvaluationMetric.HALLUCINATION: 0.25,
            EvaluationMetric.CITATION_QUALITY: 0.20,
            EvaluationMetric.LEGAL_ACCURACY: 0.20,
            EvaluationMetric.ETHICAL_COMPLIANCE: 0.10
        }

        weighted_sum = 0.0
        total_weight = 0.0

        for metric, result in individual_scores.items():
            if metric in weights:
                weight = weights[metric]
                weighted_sum += result.score * weight
                total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def _requires_human_oversight(
        self,
        individual_scores: Dict[EvaluationMetric, EvaluationResult],
        overall_score: float
    ) -> bool:
        """
        Determine if human oversight is required.

        Args:
            individual_scores: Individual evaluation results
            overall_score: Overall score

        Returns:
            True if human oversight is required
        """
        # Require oversight if overall score is below threshold
        if overall_score < self.overall_confidence_threshold:
            return True

        # Require oversight if any individual metric requires review
        for result in individual_scores.values():
            if result.requires_human_review:
                return True

        return False

    def _generate_refinement_suggestions(
        self,
        individual_scores: Dict[EvaluationMetric, EvaluationResult]
    ) -> List[str]:
        """
        Generate suggestions for improving the response.

        Args:
            individual_scores: Individual evaluation results

        Returns:
            List of refinement suggestions
        """
        suggestions = []

        for metric, result in individual_scores.items():
            if result.score < 0.7:  # Below good threshold
                if metric == EvaluationMetric.FAITHFULNESS:
                    suggestions.append("Improve alignment with source documents and verify all claims")
                elif metric == EvaluationMetric.HALLUCINATION:
                    suggestions.append("Remove unsupported claims and verify all factual statements")
                elif metric == EvaluationMetric.CITATION_QUALITY:
                    suggestions.append("Improve citation format and ensure all claims are properly cited")
                elif metric == EvaluationMetric.LEGAL_ACCURACY:
                    suggestions.append("Review legal reasoning and ensure jurisdiction-specific accuracy")
                elif metric == EvaluationMetric.ETHICAL_COMPLIANCE:
                    suggestions.append("Add appropriate disclaimers and ensure ethical compliance")

        return suggestions
