"""
AiLex Research Agent State Management

This module defines the state and data structures for the AiLex research agent.

Key Features:
- Supports AI-powered legal research for solo and small-firm lawyers.
- Automatically classifies user queries by type and data source needs.
- Integrates with CopilotKit Cloud for seamless frontend-backend communication.
- Tracks query intent, user context, jurisdiction (state), and practice area.
- Supports both legal database search and web search paths.
- Provides citation tracking and formatting for search results.

Query Classification:
- Queries are classified by type: "legal_research", "web_search", "deep_research", or "non_legal".
- Legal research queries are further classified by data source: "public", "private", or "hybrid".
- Classification determines the appropriate search path and access controls.

Jurisdiction & Practice Area:
- The `jurisdiction` field (default: "texas") determines which state's public law sources are searched.
- The `practice_areas` field reflects the current area(s) of law (e.g., "personal_injury", "criminal_defense").
- These should be set from the user's subscription data or profile and passed into the agent's state at query time.
- The agent's retrieval logic respects these fields to ensure only relevant public law is accessed.

CopilotKit Integration:
- The agent is invoked via CopilotKit Cloud, with user/session info and query context passed from the frontend.
- The first node in the research graph classifies the query and sets routing information.
- All downstream retrieval and synthesis respects the jurisdiction and practice area as set in the state.

See also: agent.py, graph.py
"""
import logging
import uuid
from dataclasses import dataclass, field
from typing import (
    Any,
    Dict,
    List,
    Literal,
    Optional,
    Set,
)

from pydantic import BaseModel

from shared.core.state import AiLexState
from shared.core.state import UserContext as BaseUserContext


# Define Document class for compatibility
class Document:
    """Simple document class to replace langchain_core.documents.Document."""
    def __init__(self, page_content: str, metadata: Optional[Dict[str, Any]] = None):
        self.page_content = page_content
        self.metadata = metadata or {}

# Configure logger
logger = logging.getLogger(__name__)

# Type definitions
DataSourceType = Literal["public", "private", "hybrid"]
ResearchQueryType = Literal["legal_research", "web_search", "deep_research", "non_legal"]
UserRole = Literal["partner", "attorney", "paralegal", "staff"]
PracticeArea = Literal["personal_injury", "criminal_defense", "family_law"]

# For backward compatibility
QueryType = DataSourceType


@dataclass(kw_only=True)
class UserContext(BaseUserContext):
    """
    User context from authenticated session.

    This class extends the base UserContext with research-specific methods.
    """
    role: UserRole
    user_id: str
    tenant_id: str
    assigned_case_ids: List[str] = field(default_factory=list)
    settings: Dict[str, Any] = field(default_factory=dict)

    def can_access_case(self, case_id: str) -> bool:
        """
        Check if user can access a specific case.

        Args:
            case_id: The ID of the case to check

        Returns:
            bool: True if the user can access the case, False otherwise
        """
        if self.role == "partner":
            return True
        return case_id in self.assigned_case_ids

    def can_access_sensitive_data(self) -> bool:
        """
        Check if user can access sensitive case details.

        Returns:
            bool: True if the user can access sensitive data, False otherwise
        """
        return self.role in ["partner", "attorney"]


@dataclass(kw_only=True)
class QueryState:
    """
    State for a single query operation.

    This class represents the state of a single query operation, including
    the query text, user context, and query type.
    """
    query: str
    user_context: UserContext
    query_type: QueryType
    jurisdiction: Optional[str] = None
    case_id: Optional[str] = None  # If querying about a specific case

    def validate_access(self) -> bool:
        """
        Validate user has access to requested data.

        Returns:
            bool: True if the user has access to the requested data, False otherwise
        """
        if self.query_type == "public":
            return True
        if self.case_id and not self.user_context.can_access_case(self.case_id):
            return False
        if (
            self.query_type == "private"
            and not self.user_context.can_access_sensitive_data()
        ):
            return False
        return True


@dataclass(kw_only=True)
class ResearchState(AiLexState):
    """
    State for the research process, including query classification, search results, and citations.

    This class tracks the complete state of a research query from initial classification
    through search execution and result formatting. It supports both legal database search
    and web search paths, with appropriate citation tracking for each.

    Attributes:
        question: The original research question from the user
        user_context: User information including tenant, role, and permissions
        jurisdiction: State for legal research (e.g., "texas", "florida")
        practice_areas: Legal practice areas from user's subscription
        queries: Generated search queries for vector search
        keyword_query: Generated keyword/boolean search query
        query_type: Type of research query (legal_research, web_search, etc.)
        data_source: Type of data source needed (public, private, hybrid)
        inferred_mode: Legacy field for backward compatibility
        query_id: Unique identifier for this research query
        case_id: Associated case ID if relevant
        legal_documents: Retrieved legal documents from vector search
        case_documents: Retrieved case-specific documents
        web_search_results: Results from web search (if web_search path)
        citations: List of formatted citations for all sources
        citation_map: Mapping of citation numbers to source information
        rerank_scores: Relevance scores from reranking
        search_metadata: Additional metadata about the search process
        answer: Generated answer text
    """
    # Core fields (required)
    question: str
    user_context: UserContext

    # Context fields
    jurisdiction: str = "texas"  # State for legal research (e.g., "texas", "florida")
    practice_areas: Set[PracticeArea] = field(default_factory=set)  # Legal practice areas (from subscription)

    # Query information
    queries: List[str] = field(default_factory=list)  # Vector search queries
    keyword_query: Optional[str] = None  # Keyword/boolean search query
    query_type: Optional[ResearchQueryType] = None  # Type of research query
    data_source: Optional[DataSourceType] = None  # Type of data source needed
    inferred_mode: Optional[QueryType] = None  # Legacy field for backward compatibility
    query_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    case_id: Optional[str] = None

    # Search results
    legal_documents: List[Document] = field(default_factory=list)
    case_documents: List[Document] = field(default_factory=list)
    web_search_results: List[Dict[str, Any]] = field(default_factory=list)

    # Citation tracking
    citations: List[Dict[str, Any]] = field(default_factory=list)
    citation_map: Dict[int, Dict[str, Any]] = field(default_factory=dict)

    # Search metadata
    rerank_scores: List[float] = field(default_factory=list)
    search_metadata: Dict[str, Any] = field(default_factory=dict)

    # SELF-RAG and confidence flags
    low_confidence_rerank: bool = False  # Flag for low confidence reranking results
    low_confidence_classification: bool = False  # Flag for low confidence classification
    low_confidence_graph: bool = False  # Flag for low confidence graph expansion results
    low_confidence_clusters: bool = False  # Flag for low confidence community cluster results
    graph_clusters: int = 0  # Number of detected community clusters

    # Generated answer
    answer: Optional[str] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ResearchState":
        """
        Create a state from a dictionary.

        Args:
            data: A dictionary representation of the state

        Returns:
            A ResearchState instance
        """
        # Extract question from messages if not provided
        question = data.get("question", "")
        if not question and "messages" in data:
            for msg in reversed(data.get("messages", [])):
                if msg.get("role") == "user":
                    question = msg.get("content", "")
                    break

        # Create UserContext from dict
        user_context_dict = data.get("user_context", {})
        user_context = UserContext(**user_context_dict) if user_context_dict else UserContext(
            user_id="",
            tenant_id="",
            role="staff",
            assigned_case_ids=[],
            settings={}
        )

        # Create the state
        return cls(
            question=question,
            user_context=user_context,
            messages=data.get("messages", []),
            next=data.get("next"),
            agent_id=data.get("agent_id"),
            thread_id=data.get("thread_id", str(uuid.uuid4())),
            metadata=data.get("metadata", {}),
            jurisdiction=data.get("jurisdiction", "texas"),
            practice_areas=set(data.get("practice_areas", [])),
            queries=data.get("queries", []),
            keyword_query=data.get("keyword_query"),
            query_type=data.get("query_type"),
            data_source=data.get("data_source"),
            inferred_mode=data.get("inferred_mode"),
            query_id=data.get("query_id", str(uuid.uuid4())),
            case_id=data.get("case_id"),
            legal_documents=data.get("legal_documents", []),
            case_documents=data.get("case_documents", []),
            web_search_results=data.get("web_search_results", []),
            citations=data.get("citations", []),
            citation_map=data.get("citation_map", {}),
            rerank_scores=data.get("rerank_scores", []),
            search_metadata=data.get("search_metadata", {}),
            low_confidence_rerank=data.get("low_confidence_rerank", False),
            low_confidence_classification=data.get("low_confidence_classification", False),
            low_confidence_graph=data.get("low_confidence_graph", False),
            low_confidence_clusters=data.get("low_confidence_clusters", False),
            graph_clusters=data.get("graph_clusters", 0),
            answer=data.get("answer")
        )

    def can_view_results(self) -> bool:
        """
        Check if user can view the search results based on permissions.

        Returns:
            bool: True if the user has permission to view results, False otherwise
        """
        # Public data is always accessible
        if not self.data_source:
            return True
        if self.data_source == "public":
            return True

        # Handle both dictionary and object user contexts
        if isinstance(self.user_context, dict):
            # Case-specific access check
            if self.case_id:
                case_ids = self.user_context.get("assigned_case_ids", [])
                role = self.user_context.get("role", "")
                if role != "partner" and self.case_id not in case_ids:
                    return False

            # Private data access check
            if self.data_source == "private":
                role = self.user_context.get("role", "")
                if role not in ["partner", "attorney"]:
                    return False
        else:
            # Case-specific access check
            if self.case_id and not self.user_context.can_access_case(self.case_id):
                return False

            # Private data access check
            if (
                self.data_source == "private"
                and not self.user_context.can_access_sensitive_data()
            ):
                return False

        return True

    def get_formatted_citations(self) -> List[Dict[str, Any]]:
        """
        Get formatted citations for inclusion in the response.

        Returns:
            List[Dict[str, Any]]: List of formatted citation objects
        """
        return self.citations

    def get_evaluation_metadata(self) -> Dict[str, Any]:
        """
        Get evaluation-related metadata from search_metadata.

        Returns:
            Dict containing evaluation metrics and agentic execution data
        """
        evaluation_data = {}

        # Extract agentic metadata
        if "agentic_metadata" in self.search_metadata:
            evaluation_data["agentic"] = self.search_metadata["agentic_metadata"]

        # Extract evaluation scores from iterations
        evaluation_scores = {}
        for key, value in self.search_metadata.items():
            if key.startswith("iteration_") and key.endswith("_evaluation"):
                iteration_num = key.split("_")[1]
                evaluation_scores[f"iteration_{iteration_num}"] = value

        if evaluation_scores:
            evaluation_data["iteration_evaluations"] = evaluation_scores

        # Extract other evaluation-related flags
        evaluation_flags = {
            "human_oversight_required": self.search_metadata.get("human_oversight_required", False),
            "low_confidence_rerank": self.low_confidence_rerank,
            "low_confidence_classification": self.low_confidence_classification,
            "low_confidence_graph": self.low_confidence_graph,
            "low_confidence_clusters": self.low_confidence_clusters
        }
        evaluation_data["confidence_flags"] = evaluation_flags

        return evaluation_data

    def set_evaluation_result(self, evaluation_data: Dict[str, Any]) -> None:
        """
        Store evaluation results in search_metadata.

        Args:
            evaluation_data: Dictionary containing evaluation results
        """
        if "evaluation_results" not in self.search_metadata:
            self.search_metadata["evaluation_results"] = {}

        self.search_metadata["evaluation_results"].update(evaluation_data)

    def requires_human_oversight(self) -> bool:
        """
        Check if this research result requires human oversight.

        Returns:
            bool: True if human oversight is required
        """
        # Check explicit flag
        if self.search_metadata.get("human_oversight_required", False):
            return True

        # Check confidence flags
        confidence_issues = sum([
            self.low_confidence_rerank,
            self.low_confidence_classification,
            self.low_confidence_graph,
            self.low_confidence_clusters
        ])

        # Require oversight if multiple confidence issues
        if confidence_issues >= 2:
            return True

        # Check evaluation scores if available
        agentic_meta = self.search_metadata.get("agentic_metadata", {})
        final_score = agentic_meta.get("final_score", 1.0)

        return final_score < 0.7

    def get_quality_summary(self) -> Dict[str, Any]:
        """
        Get a summary of research quality metrics.

        Returns:
            Dict containing quality summary
        """
        agentic_meta = self.search_metadata.get("agentic_metadata", {})

        return {
            "final_score": agentic_meta.get("final_score", None),
            "iterations_completed": agentic_meta.get("iterations_completed", 0),
            "execution_time": agentic_meta.get("total_execution_time", 0),
            "requires_oversight": self.requires_human_oversight(),
            "confidence_issues": {
                "rerank": self.low_confidence_rerank,
                "classification": self.low_confidence_classification,
                "graph": self.low_confidence_graph,
                "clusters": self.low_confidence_clusters
            },
            "sources_found": len(self.legal_documents) + len(self.case_documents),
            "citations_count": len(self.citations)
        }


class Citation(BaseModel):
    """
    Structure for a single citation.

    This model represents a citation to a legal document, case, or web source.
    It includes metadata about the source and its relevance to the query.
    """
    id: str
    title: str
    url: Optional[str] = None
    source_type: str  # "legal", "case", "web", etc.
    jurisdiction: Optional[str] = None
    citation_text: Optional[str] = None
    relevance_score: Optional[float] = None
    metadata: Dict[str, Any] = {}


class SearchResult(BaseModel):
    """
    Structure for search results.

    This model represents the complete results of a research query,
    including the answer, citations, and metadata about the search process.
    """
    query_type: ResearchQueryType
    data_source: Optional[DataSourceType] = None
    answer: str
    citations: List[Citation] = []
    legal_citations: List[str] = []  # Legacy field
    case_references: List[str] = []  # Legacy field
    summary: Optional[str] = None
    recommendations: List[str] = []
    chunk_ids: List[str] = []  # IDs of relevant chunks for fetching full text
    sensitive_data: bool = False  # If true, only attorneys/partners can view
    audit_log: Dict[str, Any] = {}  # Track who accessed what
    search_metadata: Dict[str, Any] = {}
