"""
AiLex Research Agent Monitoring and Metrics

This module implements comprehensive monitoring, performance tracking, and ethical
compliance monitoring with audit trails for the Research Agent.

Key Features:
- Performance metrics tracking (latency, throughput, success rates)
- Ethical compliance monitoring and audit trails
- Quality metrics aggregation and trending
- Error tracking and alerting
- Usage analytics and insights
- Compliance reporting for legal standards
- Real-time dashboards and alerts
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta

from backend.agents.interactive.research.state import ResearchState
from backend.agents.interactive.research.evaluators import ComprehensiveEvaluation, EvaluationMetric
from backend.agents.interactive.research.agentic_wrapper import AgenticExecutionResult

# Configure logger
logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics tracked."""
    PERFORMANCE = "performance"
    QUALITY = "quality"
    ETHICAL_COMPLIANCE = "ethical_compliance"
    USAGE = "usage"
    ERROR = "error"
    AUDIT = "audit"


class ComplianceLevel(Enum):
    """Ethical compliance levels."""
    COMPLIANT = "compliant"
    WARNING = "warning"
    VIOLATION = "violation"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """Performance tracking metric."""
    metric_id: str
    timestamp: float
    user_id: str
    tenant_id: str
    query_id: str
    execution_time: float
    iterations_count: int
    tokens_used: int
    api_calls_count: int
    cache_hit_rate: float
    memory_usage: float
    success: bool
    error_type: Optional[str] = None


@dataclass
class QualityMetric:
    """Quality assessment metric."""
    metric_id: str
    timestamp: float
    user_id: str
    tenant_id: str
    query_id: str
    overall_score: float
    faithfulness_score: float
    hallucination_score: float
    citation_quality_score: float
    legal_accuracy_score: float
    ethical_compliance_score: float
    human_oversight_required: bool
    refinement_iterations: int


@dataclass
class EthicalComplianceMetric:
    """Ethical compliance tracking."""
    metric_id: str
    timestamp: float
    user_id: str
    tenant_id: str
    query_id: str
    compliance_level: ComplianceLevel
    aba_rule_compliance: Dict[str, bool]
    disclaimer_present: bool
    human_supervision_flagged: bool
    confidentiality_protected: bool
    competence_demonstrated: bool
    audit_trail_complete: bool
    violation_details: Optional[str] = None


@dataclass
class UsageMetric:
    """Usage analytics metric."""
    metric_id: str
    timestamp: float
    user_id: str
    tenant_id: str
    query_type: str
    jurisdiction: str
    practice_areas: List[str]
    query_complexity: str
    processing_mode: str  # sync/async
    user_satisfaction: Optional[float] = None


@dataclass
class AuditTrailEntry:
    """Audit trail entry for compliance."""
    entry_id: str
    timestamp: float
    user_id: str
    tenant_id: str
    query_id: str
    action: str
    details: Dict[str, Any]
    compliance_impact: str
    reviewer_required: bool
    retention_period: int  # days


class ResearchAgentMonitor:
    """
    Comprehensive monitoring system for the Research Agent.
    
    Tracks performance, quality, ethical compliance, and usage metrics
    with audit trails for legal accountability.
    """
    
    def __init__(self):
        """Initialize the monitoring system."""
        self.metrics_storage = {}  # In-memory storage (replace with persistent storage)
        self.audit_trail = []
        self.alert_thresholds = {
            "performance": {
                "max_execution_time": 300,  # 5 minutes
                "min_success_rate": 0.95,
                "max_error_rate": 0.05
            },
            "quality": {
                "min_overall_score": 0.7,
                "max_human_oversight_rate": 0.3,
                "min_citation_quality": 0.6
            },
            "ethical": {
                "max_violation_rate": 0.01,
                "min_compliance_score": 0.9
            }
        }
        
    async def track_performance(
        self,
        state: ResearchState,
        execution_result: AgenticExecutionResult,
        tokens_used: int = 0,
        api_calls: int = 0,
        cache_hits: int = 0,
        total_requests: int = 1
    ) -> None:
        """
        Track performance metrics for a research execution.
        
        Args:
            state: Research state
            execution_result: Agentic execution result
            tokens_used: Number of tokens consumed
            api_calls: Number of API calls made
            cache_hits: Number of cache hits
            total_requests: Total requests made
        """
        metric = PerformanceMetric(
            metric_id=f"perf_{state.query_id}_{int(time.time())}",
            timestamp=time.time(),
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            query_id=state.query_id,
            execution_time=execution_result.total_execution_time,
            iterations_count=len(execution_result.iterations),
            tokens_used=tokens_used,
            api_calls_count=api_calls,
            cache_hit_rate=cache_hits / max(total_requests, 1),
            memory_usage=0.0,  # TODO: Implement memory tracking
            success=not execution_result.requires_human_oversight,
            error_type=None if not execution_result.requires_human_oversight else "oversight_required"
        )
        
        await self._store_metric(MetricType.PERFORMANCE, metric)
        await self._check_performance_alerts(metric)
        
        logger.info(f"Performance tracked: {execution_result.total_execution_time:.2f}s, {len(execution_result.iterations)} iterations")
    
    async def track_quality(
        self,
        state: ResearchState,
        evaluation: ComprehensiveEvaluation,
        refinement_iterations: int
    ) -> None:
        """
        Track quality metrics for a research result.
        
        Args:
            state: Research state
            evaluation: Comprehensive evaluation
            refinement_iterations: Number of refinement iterations
        """
        metric = QualityMetric(
            metric_id=f"qual_{state.query_id}_{int(time.time())}",
            timestamp=time.time(),
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            query_id=state.query_id,
            overall_score=evaluation.overall_score,
            faithfulness_score=evaluation.individual_scores.get(EvaluationMetric.FAITHFULNESS, type('obj', (object,), {'score': 0.0})).score,
            hallucination_score=evaluation.individual_scores.get(EvaluationMetric.HALLUCINATION, type('obj', (object,), {'score': 0.0})).score,
            citation_quality_score=evaluation.individual_scores.get(EvaluationMetric.CITATION_QUALITY, type('obj', (object,), {'score': 0.0})).score,
            legal_accuracy_score=evaluation.individual_scores.get(EvaluationMetric.LEGAL_ACCURACY, type('obj', (object,), {'score': 0.0})).score,
            ethical_compliance_score=evaluation.individual_scores.get(EvaluationMetric.ETHICAL_COMPLIANCE, type('obj', (object,), {'score': 0.0})).score,
            human_oversight_required=evaluation.requires_human_oversight,
            refinement_iterations=refinement_iterations
        )
        
        await self._store_metric(MetricType.QUALITY, metric)
        await self._check_quality_alerts(metric)
        
        logger.info(f"Quality tracked: {evaluation.overall_score:.2f} overall, oversight: {evaluation.requires_human_oversight}")
    
    async def track_ethical_compliance(
        self,
        state: ResearchState,
        evaluation: ComprehensiveEvaluation
    ) -> None:
        """
        Track ethical compliance metrics.
        
        Args:
            state: Research state
            evaluation: Comprehensive evaluation
        """
        ethical_result = evaluation.individual_scores.get(EvaluationMetric.ETHICAL_COMPLIANCE)
        
        # Determine compliance level
        compliance_level = ComplianceLevel.COMPLIANT
        if ethical_result and ethical_result.score < 0.9:
            compliance_level = ComplianceLevel.WARNING
        if ethical_result and ethical_result.score < 0.7:
            compliance_level = ComplianceLevel.VIOLATION
        if ethical_result and ethical_result.score < 0.5:
            compliance_level = ComplianceLevel.CRITICAL
        
        # Check ABA rule compliance
        aba_compliance = {
            "rule_1_1_competence": ethical_result.score > 0.7 if ethical_result else False,
            "rule_1_6_confidentiality": True,  # Assume protected unless flagged
            "rule_3_3_candor": not evaluation.requires_human_oversight,
            "rule_1_4_communication": True,  # Assume clear communication
            "rule_5_3_supervision": evaluation.requires_human_oversight
        }
        
        metric = EthicalComplianceMetric(
            metric_id=f"eth_{state.query_id}_{int(time.time())}",
            timestamp=time.time(),
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            query_id=state.query_id,
            compliance_level=compliance_level,
            aba_rule_compliance=aba_compliance,
            disclaimer_present=True,  # TODO: Check for actual disclaimers
            human_supervision_flagged=evaluation.requires_human_oversight,
            confidentiality_protected=True,  # TODO: Implement confidentiality check
            competence_demonstrated=ethical_result.score > 0.7 if ethical_result else False,
            audit_trail_complete=True,
            violation_details=ethical_result.explanation if ethical_result and ethical_result.score < 0.7 else None
        )
        
        await self._store_metric(MetricType.ETHICAL_COMPLIANCE, metric)
        await self._check_ethical_alerts(metric)
        
        # Create audit trail entry
        await self._create_audit_entry(
            state, "ethical_compliance_check", 
            {"compliance_level": compliance_level.value, "score": ethical_result.score if ethical_result else 0.0},
            "compliance_monitoring"
        )
        
        logger.info(f"Ethical compliance tracked: {compliance_level.value}")
    
    async def track_usage(
        self,
        state: ResearchState,
        processing_mode: str = "sync"
    ) -> None:
        """
        Track usage analytics.
        
        Args:
            state: Research state
            processing_mode: Processing mode (sync/async)
        """
        # Determine query complexity
        complexity = "simple"
        if len(state.question) > 200:
            complexity = "complex"
        elif len(state.practice_areas) > 2:
            complexity = "medium"
        
        metric = UsageMetric(
            metric_id=f"usage_{state.query_id}_{int(time.time())}",
            timestamp=time.time(),
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            query_type=state.query_type or "research",
            jurisdiction=state.jurisdiction,
            practice_areas=list(state.practice_areas),
            query_complexity=complexity,
            processing_mode=processing_mode
        )
        
        await self._store_metric(MetricType.USAGE, metric)
        
        logger.debug(f"Usage tracked: {complexity} {processing_mode} query in {state.jurisdiction}")
    
    async def create_audit_trail(
        self,
        state: ResearchState,
        action: str,
        details: Dict[str, Any],
        compliance_impact: str = "low",
        reviewer_required: bool = False
    ) -> str:
        """
        Create an audit trail entry.
        
        Args:
            state: Research state
            action: Action performed
            details: Action details
            compliance_impact: Compliance impact level
            reviewer_required: Whether review is required
            
        Returns:
            Audit entry ID
        """
        return await self._create_audit_entry(
            state, action, details, compliance_impact, reviewer_required
        )
    
    async def get_performance_summary(
        self,
        tenant_id: str,
        hours: int = 24
    ) -> Dict[str, Any]:
        """
        Get performance summary for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            hours: Hours to look back
            
        Returns:
            Performance summary
        """
        cutoff_time = time.time() - (hours * 3600)
        
        # Filter metrics for tenant and time period
        perf_metrics = [
            m for m in self.metrics_storage.get(MetricType.PERFORMANCE, [])
            if m.tenant_id == tenant_id and m.timestamp > cutoff_time
        ]
        
        if not perf_metrics:
            return {"error": "No performance data available"}
        
        # Calculate summary statistics
        total_queries = len(perf_metrics)
        avg_execution_time = sum(m.execution_time for m in perf_metrics) / total_queries
        success_rate = sum(1 for m in perf_metrics if m.success) / total_queries
        avg_iterations = sum(m.iterations_count for m in perf_metrics) / total_queries
        
        return {
            "period_hours": hours,
            "total_queries": total_queries,
            "average_execution_time": avg_execution_time,
            "success_rate": success_rate,
            "average_iterations": avg_iterations,
            "total_tokens_used": sum(m.tokens_used for m in perf_metrics),
            "total_api_calls": sum(m.api_calls_count for m in perf_metrics)
        }
    
    async def _store_metric(self, metric_type: MetricType, metric: Any) -> None:
        """Store a metric in the storage system."""
        if metric_type not in self.metrics_storage:
            self.metrics_storage[metric_type] = []
        
        self.metrics_storage[metric_type].append(metric)
        
        # Keep only last 1000 metrics per type (simple cleanup)
        if len(self.metrics_storage[metric_type]) > 1000:
            self.metrics_storage[metric_type] = self.metrics_storage[metric_type][-1000:]
    
    async def _create_audit_entry(
        self,
        state: ResearchState,
        action: str,
        details: Dict[str, Any],
        compliance_impact: str,
        reviewer_required: bool = False
    ) -> str:
        """Create an audit trail entry."""
        entry_id = f"audit_{state.query_id}_{int(time.time())}"
        
        entry = AuditTrailEntry(
            entry_id=entry_id,
            timestamp=time.time(),
            user_id=state.user_context.user_id,
            tenant_id=state.user_context.tenant_id,
            query_id=state.query_id,
            action=action,
            details=details,
            compliance_impact=compliance_impact,
            reviewer_required=reviewer_required,
            retention_period=2555  # 7 years for legal compliance
        )
        
        self.audit_trail.append(entry)
        
        # Keep audit trail manageable
        if len(self.audit_trail) > 10000:
            self.audit_trail = self.audit_trail[-10000:]
        
        return entry_id
    
    async def _check_performance_alerts(self, metric: PerformanceMetric) -> None:
        """Check performance thresholds and send alerts."""
        thresholds = self.alert_thresholds["performance"]
        
        if metric.execution_time > thresholds["max_execution_time"]:
            logger.warning(f"Performance alert: Execution time {metric.execution_time:.2f}s exceeds threshold")
        
        if not metric.success:
            logger.warning(f"Performance alert: Query {metric.query_id} failed")
    
    async def _check_quality_alerts(self, metric: QualityMetric) -> None:
        """Check quality thresholds and send alerts."""
        thresholds = self.alert_thresholds["quality"]
        
        if metric.overall_score < thresholds["min_overall_score"]:
            logger.warning(f"Quality alert: Overall score {metric.overall_score:.2f} below threshold")
        
        if metric.human_oversight_required:
            logger.info(f"Quality alert: Human oversight required for query {metric.query_id}")
    
    async def _check_ethical_alerts(self, metric: EthicalComplianceMetric) -> None:
        """Check ethical compliance thresholds and send alerts."""
        if metric.compliance_level in [ComplianceLevel.VIOLATION, ComplianceLevel.CRITICAL]:
            logger.error(f"Ethical alert: {metric.compliance_level.value} detected for query {metric.query_id}")
        elif metric.compliance_level == ComplianceLevel.WARNING:
            logger.warning(f"Ethical alert: Compliance warning for query {metric.query_id}")


# Global monitoring instance
research_monitor = ResearchAgentMonitor()
