"""
Integration Tests for Long-Context Hybrid Search

This module contains integration tests for the complete hybrid search workflow,
performance validation, and backward compatibility testing.
"""

import asyncio
import time
import pytest
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from backend.agents.interactive.research.nodes import (
    hybrid_vector_search,
    configure_search_metadata,
    log_search_metrics
)
from backend.agents.interactive.research.state import ResearchState, Document, UserContext
from backend.agents.interactive.research.graph import create_research_graph


class TestHybridSearchIntegration:
    """Integration tests for hybrid search workflow."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.user_context = UserContext(
            user_id="test_user",
            tenant_id="test_tenant",
            role="attorney"
        )
        
        # Create sample legal documents
        self.sample_documents = [
            Document(
                page_content="""
                In the case of <PERSON> v. <PERSON>, 123 F.3d 456 (5th Cir. 2020), the court held that 
                personal injury claims in Texas require proof of four elements: duty, breach, 
                causation, and damages. The plaintiff must establish that the defendant owed a 
                legal duty to the plaintiff, that the defendant breached that duty through action 
                or inaction, that the breach was the proximate cause of the plaintiff's injuries, 
                and that the plaintiff suffered actual damages as a result.
                
                The court further noted that in medical malpractice cases, the standard of care 
                is determined by what a reasonably prudent healthcare provider would do under 
                similar circumstances. Expert testimony is typically required to establish both 
                the applicable standard of care and any deviation from that standard.
                
                Damages in personal injury cases may include economic damages such as medical 
                expenses, lost wages, and property damage, as well as non-economic damages 
                such as pain and suffering, emotional distress, and loss of consortium.
                """ * 10,  # Make it long enough to trigger summarization
                metadata={
                    "id": "smith_v_jones_2020",
                    "title": "Smith v. Jones - Personal Injury Elements",
                    "jurisdiction": "texas",
                    "document_type": "case_law",
                    "citation": "123 F.3d 456 (5th Cir. 2020)",
                    "source": "laws_api"
                }
            ),
            Document(
                page_content="""
                Texas Civil Practice and Remedies Code § 16.003 establishes the statute of 
                limitations for personal injury claims. Generally, a person must bring suit 
                for personal injury not later than two years after the day the cause of 
                action accrues.
                
                The cause of action accrues on the date the injury occurs, not when the 
                plaintiff discovers the injury. However, there are exceptions to this rule, 
                including the discovery rule for certain types of cases where the injury 
                is not immediately apparent.
                
                Medical malpractice claims are subject to special rules under Chapter 74 
                of the Civil Practice and Remedies Code, including requirements for expert 
                reports and specific notice provisions.
                """,
                metadata={
                    "id": "tex_civ_prac_16_003",
                    "title": "Texas Statute of Limitations - Personal Injury",
                    "jurisdiction": "texas",
                    "document_type": "statute",
                    "citation": "Tex. Civ. Prac. & Rem. Code § 16.003",
                    "source": "laws_api"
                }
            ),
            Document(
                page_content="""
                The Texas Medical Liability Act, codified in Chapter 74 of the Civil Practice 
                and Remedies Code, governs medical malpractice claims in Texas. This comprehensive 
                statute establishes specific procedures and requirements that must be followed 
                in medical malpractice litigation.
                
                Key provisions include the requirement for an expert report under Section 74.351, 
                which must be served within 120 days after the defendant files an answer. The 
                expert report must provide a fair summary of the expert's opinions and the basis 
                for those opinions regarding applicable standards of care, how those standards 
                were violated, and how the violations proximately caused the damages claimed.
                
                The Act also establishes caps on non-economic damages in medical malpractice 
                cases, with different limits for different types of healthcare providers and 
                institutions.
                """,
                metadata={
                    "id": "tex_med_liability_act",
                    "title": "Texas Medical Liability Act",
                    "jurisdiction": "texas",
                    "document_type": "statute",
                    "citation": "Tex. Civ. Prac. & Rem. Code Ch. 74",
                    "source": "laws_api"
                }
            )
        ]
    
    @pytest.mark.asyncio
    async def test_complete_hybrid_search_workflow(self):
        """Test the complete hybrid search workflow end-to-end."""
        state = ResearchState(
            question="What are the elements of a personal injury claim in Texas?",
            user_context=self.user_context,
            queries=["personal injury elements Texas", "negligence duty breach causation damages"],
            jurisdiction="texas",
            practice_areas={"personal_injury"}
        )
        
        # Configure for long-context processing
        configure_search_metadata(state, chunk_strategy="full_sections")
        
        # Mock the vector_search function to return our sample documents
        with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
            mock_vector_search.return_value = {"status": "success"}
            state.legal_documents = self.sample_documents.copy()
            
            # Mock the LLM for summarization
            with patch('backend.agents.interactive.research.nodes.generate_completion') as mock_llm:
                mock_response = Mock()
                mock_response.content = "Summarized legal content focusing on key elements and precedents."
                mock_llm.return_value = mock_response
                
                config = Mock()
                result = await hybrid_vector_search(state, config)
                
                # Verify successful completion
                assert result["status"] == "success"
                assert result["next"] == "graph_expand"
                
                # Verify hybrid search metadata
                assert "hybrid_search" in state.search_metadata
                hybrid_info = state.search_metadata["hybrid_search"]
                assert hybrid_info["dense_results"] > 0
                assert hybrid_info["final_results"] > 0
                
                # Verify long-context processing occurred
                if state.search_metadata.get("summarization"):
                    summarization_info = state.search_metadata["summarization"]
                    assert summarization_info["documents_summarized"] >= 0
                    assert summarization_info["compression_ratio"] >= 0
    
    @pytest.mark.asyncio
    async def test_performance_with_large_documents(self):
        """Test performance impact with large documents."""
        # Create a large document that will trigger summarization
        large_content = """
        This is a very long legal document that contains extensive case law analysis,
        statutory interpretation, and detailed legal reasoning. It includes multiple
        citations, cross-references, and comprehensive coverage of legal principles.
        """ * 500  # Create ~50KB document
        
        large_doc = Document(
            page_content=large_content,
            metadata={
                "id": "large_legal_doc",
                "title": "Comprehensive Legal Analysis",
                "jurisdiction": "texas",
                "document_type": "case_law",
                "source": "laws_api"
            }
        )
        
        state = ResearchState(
            question="Complex legal analysis question",
            user_context=self.user_context,
            queries=["complex legal analysis"],
            jurisdiction="texas"
        )
        
        # Configure for long-context processing
        configure_search_metadata(state, chunk_strategy="full_sections")
        
        # Mock vector search to return large document
        with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
            mock_vector_search.return_value = {"status": "success"}
            state.legal_documents = [large_doc]
            
            # Mock LLM for summarization
            with patch('backend.agents.interactive.research.nodes.generate_completion') as mock_llm:
                mock_response = Mock()
                mock_response.content = "Concise summary of the large legal document."
                mock_llm.return_value = mock_response
                
                # Measure performance
                start_time = time.time()
                config = Mock()
                result = await hybrid_vector_search(state, config)
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                # Verify successful completion
                assert result["status"] == "success"
                
                # Performance should be reasonable (< 10 seconds for test)
                assert processing_time < 10.0
                
                # Verify summarization occurred
                if state.search_metadata.get("summarization"):
                    summarization_info = state.search_metadata["summarization"]
                    assert summarization_info["documents_summarized"] > 0
                    assert summarization_info["compression_ratio"] < 100  # Should be compressed
    
    @pytest.mark.asyncio
    async def test_backward_compatibility(self):
        """Test backward compatibility with existing research flows."""
        # Test with standard chunk strategy (should not trigger long-context processing)
        state = ResearchState(
            question="Standard legal question",
            user_context=self.user_context,
            queries=["legal question"],
            jurisdiction="texas"
        )
        
        # Use standard configuration (default)
        configure_search_metadata(state, chunk_strategy="standard")
        
        with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
            mock_vector_search.return_value = {"status": "success"}
            state.legal_documents = self.sample_documents[:2]  # Use smaller documents
            
            config = Mock()
            result = await hybrid_vector_search(state, config)
            
            # Should complete successfully without long-context processing
            assert result["status"] == "success"
            assert result["next"] == "graph_expand"
            
            # Should not have summarization metadata
            assert state.search_metadata.get("summarization") is None
            
            # Should still have hybrid search metadata
            assert "hybrid_search" in state.search_metadata
    
    @pytest.mark.asyncio
    async def test_error_resilience(self):
        """Test system resilience to various error conditions."""
        state = ResearchState(
            question="Test question for error handling",
            user_context=self.user_context,
            queries=["test query"],
            jurisdiction="texas"
        )
        
        configure_search_metadata(state, chunk_strategy="full_sections")
        
        # Test with vector search failure
        with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
            mock_vector_search.return_value = {"status": "error", "error": "API failure"}
            
            config = Mock()
            result = await hybrid_vector_search(state, config)
            
            # Should propagate the error
            assert result["status"] == "error"
            assert "API failure" in result["error"]
        
        # Test with BM25 failure but successful dense search
        with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
            mock_vector_search.return_value = {"status": "success"}
            state.legal_documents = self.sample_documents[:1]
            
            with patch('backend.agents.interactive.research.nodes.safe_bm25_search') as mock_bm25:
                mock_bm25.return_value = []  # Simulate BM25 failure
                
                config = Mock()
                result = await hybrid_vector_search(state, config)
                
                # Should still succeed with dense results only
                assert result["status"] == "success"
                assert result["next"] == "graph_expand"
    
    def test_search_metrics_logging(self):
        """Test comprehensive search metrics logging."""
        state = ResearchState(
            question="Test question",
            user_context=self.user_context
        )
        
        # Set up search metadata
        state.legal_documents = self.sample_documents
        state.search_metadata = {
            "hybrid_search": {
                "dense_results": 3,
                "sparse_results": 2,
                "combined_results": 4,
                "final_results": 3,
                "chunk_strategy": "full_sections"
            },
            "summarization": {
                "documents_summarized": 1,
                "original_tokens": 5000,
                "summary_tokens": 1500,
                "compression_ratio": 30.0
            }
        }
        
        # Test logging (should not raise exceptions)
        log_search_metrics(state)
        
        # Verify state is not modified
        assert len(state.legal_documents) == 3
        assert "hybrid_search" in state.search_metadata
        assert "summarization" in state.search_metadata


class TestGraphIntegration:
    """Test integration with the research agent graph."""
    
    @pytest.mark.asyncio
    async def test_graph_workflow_with_hybrid_search(self):
        """Test that the graph correctly uses hybrid search."""
        # Create the research graph
        graph = create_research_graph()
        
        # Verify that the graph has the correct nodes
        assert "vector_search" in graph.nodes
        
        # The graph should be using hybrid_vector_search for the vector_search node
        # This is verified by the successful import and compilation of the graph
        assert graph is not None
    
    def test_configuration_integration(self):
        """Test configuration integration across the system."""
        user_context = UserContext(
            user_id="test_user",
            tenant_id="test_tenant",
            role="attorney"
        )
        
        state = ResearchState(
            question="Configuration test",
            user_context=user_context
        )
        
        # Test different configuration scenarios
        configurations = [
            {"chunk_strategy": "standard", "expected_long_context": False},
            {"chunk_strategy": "full_sections", "expected_long_context": True},
        ]
        
        for config in configurations:
            configure_search_metadata(
                state,
                chunk_strategy=config["chunk_strategy"],
                long_context_model="gemini-1.5-pro",
                token_threshold=4000
            )
            
            from backend.agents.interactive.research.nodes import should_use_long_context
            assert should_use_long_context(state) == config["expected_long_context"]


@pytest.mark.asyncio
async def test_end_to_end_performance():
    """End-to-end performance test with realistic data."""
    user_context = UserContext(
        user_id="perf_test_user",
        tenant_id="perf_test_tenant",
        role="attorney"
    )
    
    # Create multiple documents of varying sizes
    documents = []
    for i in range(10):
        content_size = 1000 + (i * 500)  # Varying document sizes
        content = f"Legal document {i} content. " * content_size
        
        doc = Document(
            page_content=content,
            metadata={
                "id": f"perf_doc_{i}",
                "title": f"Performance Test Document {i}",
                "jurisdiction": "texas",
                "source": "laws_api"
            }
        )
        documents.append(doc)
    
    state = ResearchState(
        question="Performance test query with multiple documents",
        user_context=user_context,
        queries=["performance test", "multiple documents"],
        jurisdiction="texas"
    )
    
    configure_search_metadata(state, chunk_strategy="full_sections")
    
    with patch('backend.agents.interactive.research.nodes.vector_search') as mock_vector_search:
        mock_vector_search.return_value = {"status": "success"}
        state.legal_documents = documents
        
        with patch('backend.agents.interactive.research.nodes.generate_completion') as mock_llm:
            mock_response = Mock()
            mock_response.content = "Performance test summary."
            mock_llm.return_value = mock_response
            
            start_time = time.time()
            config = Mock()
            result = await hybrid_vector_search(state, config)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Verify successful completion
            assert result["status"] == "success"
            
            # Performance should scale reasonably
            assert processing_time < 30.0  # Should complete within 30 seconds
            
            # Verify processing occurred
            assert "hybrid_search" in state.search_metadata
            hybrid_info = state.search_metadata["hybrid_search"]
            assert hybrid_info["final_results"] > 0
