/**
 * Research Progress Widget
 * 
 * Real-time progress display for Research Agent tasks with:
 * - Live progress updates during multi-step evaluation
 * - Citation confidence indicators
 * - Human oversight alerts
 * - Task status and completion notifications
 * - Error handling and retry mechanisms
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle2, 
  Circle, 
  Clock, 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  WifiOff,
  Eye,
  BookOpen,
  Search
} from "lucide-react";
import { useResearchWebSocket, ProgressUpdate, ResearchNotification } from '@/hooks/useResearchWebSocket';

interface ResearchProgressWidgetProps {
  threadId: string;
  className?: string;
  showConnectionStatus?: boolean;
  showCitations?: boolean;
  onNotificationClick?: (notification: ResearchNotification) => void;
}

export function ResearchProgressWidget({
  threadId,
  className = "",
  showConnectionStatus = true,
  showCitations = true,
  onNotificationClick
}: ResearchProgressWidgetProps) {
  
  const {
    isConnected,
    isConnecting,
    error,
    progressUpdates,
    citations,
    notifications,
    latestProgress,
    latestNotification,
    connect,
    disconnect,
    clearNotifications
  } = useResearchWebSocket({ threadId });

  const [showAllProgress, setShowAllProgress] = useState(false);
  const [showAllNotifications, setShowAllNotifications] = useState(false);

  // Get current progress percentage
  const currentProgress = latestProgress?.progress || 0;
  const currentStep = latestProgress?.current_step || 'Initializing...';
  const currentMessage = latestProgress?.message || 'Starting research task';

  // Get connection status color
  const getConnectionStatusColor = () => {
    if (isConnected) return 'text-green-500';
    if (isConnecting) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Get connection status icon
  const getConnectionStatusIcon = () => {
    if (isConnected) return <Wifi className="w-4 h-4" />;
    if (isConnecting) return <RefreshCw className="w-4 h-4 animate-spin" />;
    return <WifiOff className="w-4 h-4" />;
  };

  // Get progress step icon
  const getProgressStepIcon = (step: string) => {
    if (step.includes('complete') || step.includes('finished')) {
      return <CheckCircle2 className="w-4 h-4 text-green-500" />;
    }
    if (step.includes('search') || step.includes('query')) {
      return <Search className="w-4 h-4 text-blue-500" />;
    }
    if (step.includes('evaluate') || step.includes('review')) {
      return <Eye className="w-4 h-4 text-purple-500" />;
    }
    if (step.includes('citation') || step.includes('reference')) {
      return <BookOpen className="w-4 h-4 text-orange-500" />;
    }
    return <Clock className="w-4 h-4 text-gray-500" />;
  };

  // Get notification severity color
  const getNotificationColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'destructive';
      case 'warning': return 'secondary';
      case 'success': return 'default';
      default: return 'outline';
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: ResearchNotification) => {
    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  };

  return (
    <Card className={`w-full max-w-md ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Research Progress</CardTitle>
          {showConnectionStatus && (
            <div className={`flex items-center gap-2 ${getConnectionStatusColor()}`}>
              {getConnectionStatusIcon()}
              <span className="text-sm">
                {isConnected ? 'Connected' : isConnecting ? 'Connecting...' : 'Disconnected'}
              </span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Connection Error */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{error}</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={connect}
                className="ml-2"
              >
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Current Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-muted-foreground">
              {Math.round(currentProgress * 100)}%
            </span>
          </div>
          <Progress value={currentProgress * 100} className="h-2" />
        </div>

        {/* Current Step */}
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          {getProgressStepIcon(currentStep)}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{currentStep}</p>
            <p className="text-xs text-muted-foreground truncate">{currentMessage}</p>
          </div>
        </div>

        {/* Recent Progress Updates */}
        {progressUpdates.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Recent Steps</span>
              {progressUpdates.length > 3 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllProgress(!showAllProgress)}
                >
                  {showAllProgress ? 'Show Less' : `Show All (${progressUpdates.length})`}
                </Button>
              )}
            </div>
            
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {(showAllProgress ? progressUpdates : progressUpdates.slice(-3))
                .reverse()
                .map((update, index) => (
                <div key={`${update.task_id}-${update.timestamp}-${index}`} 
                     className="flex items-center gap-2 text-xs">
                  <Circle className="w-2 h-2 fill-current text-blue-500" />
                  <span className="flex-1 truncate">{update.current_step}</span>
                  <span className="text-muted-foreground">
                    {Math.round(update.progress * 100)}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Citations */}
        {showCitations && citations.length > 0 && (
          <div className="space-y-2">
            <span className="text-sm font-medium">Citations Found</span>
            <div className="space-y-1">
              {citations.slice(-3).map((citation, index) => (
                <div key={`${citation.id}-${index}`} 
                     className="flex items-center gap-2 p-2 bg-muted/30 rounded text-xs">
                  <BookOpen className="w-3 h-3 text-orange-500" />
                  <span className="flex-1 truncate">{citation.source}</span>
                  <Badge 
                    variant="outline" 
                    className="text-xs"
                    style={{
                      borderColor: citation.confidence > 0.8 ? '#10b981' : 
                                  citation.confidence > 0.6 ? '#f59e0b' : '#ef4444'
                    }}
                  >
                    {Math.round(citation.confidence * 100)}%
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notifications */}
        {notifications.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Notifications</span>
              <div className="flex gap-1">
                {notifications.length > 2 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllNotifications(!showAllNotifications)}
                  >
                    {showAllNotifications ? 'Less' : `All (${notifications.length})`}
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearNotifications}
                >
                  Clear
                </Button>
              </div>
            </div>
            
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {(showAllNotifications ? notifications : notifications.slice(-2))
                .reverse()
                .map((notification, index) => (
                <Alert 
                  key={`${notification.timestamp}-${index}`}
                  variant={getNotificationColor(notification.severity) as any}
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleNotificationClick(notification)}
                >
                  <AlertDescription className="text-xs">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{notification.title}</span>
                      <Badge variant="outline" className="text-xs">
                        {notification.type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="mt-1 text-muted-foreground truncate">
                      {notification.message}
                    </p>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </div>
        )}

        {/* No Activity State */}
        {!isConnecting && !latestProgress && progressUpdates.length === 0 && (
          <div className="text-center py-4 text-muted-foreground">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No research activity yet</p>
            <p className="text-xs">Start a research query to see progress</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
