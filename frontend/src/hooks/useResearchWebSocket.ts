/**
 * React hook for WebSocket connection to Research Agent
 * 
 * This hook provides real-time communication with the Research Agent backend,
 * handling progress updates, citations, and notifications.
 * 
 * Features:
 * - Automatic connection management with authentication
 * - Real-time progress updates during research tasks
 * - Citation highlighting with confidence indicators
 * - Human oversight alerts and notifications
 * - Automatic reconnection on connection loss
 * - Error handling and fallback mechanisms
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/useAuth';

// Types for WebSocket messages
export interface ProgressUpdate {
  task_id: string;
  progress: number;
  current_step: string;
  message: string;
  timestamp: number;
  iteration?: number;
  evaluation_score?: number;
}

export interface Citation {
  id: string;
  text: string;
  source: string;
  confidence: number;
  type: string;
  jurisdiction?: string;
  url?: string;
}

export interface ResearchNotification {
  type: 'progress_update' | 'citation_highlight' | 'human_oversight' | 'task_complete' | 'error';
  severity: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  user_id: string;
  thread_id: string;
}

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export interface UseResearchWebSocketOptions {
  threadId: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface UseResearchWebSocketReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  
  // Data
  progressUpdates: ProgressUpdate[];
  citations: Citation[];
  notifications: ResearchNotification[];
  
  // Actions
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => void;
  clearNotifications: () => void;
  
  // Latest updates
  latestProgress: ProgressUpdate | null;
  latestNotification: ResearchNotification | null;
}

export function useResearchWebSocket({
  threadId,
  autoConnect = true,
  reconnectAttempts = 3,
  reconnectDelay = 1000
}: UseResearchWebSocketOptions): UseResearchWebSocketReturn {
  
  const { session } = useAuth();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progressUpdates, setProgressUpdates] = useState<ProgressUpdate[]>([]);
  const [citations, setCitations] = useState<Citation[]>([]);
  const [notifications, setNotifications] = useState<ResearchNotification[]>([]);
  const [latestProgress, setLatestProgress] = useState<ProgressUpdate | null>(null);
  const [latestNotification, setLatestNotification] = useState<ResearchNotification | null>(null);

  // Get WebSocket URL
  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = process.env.NODE_ENV === 'production' 
      ? 'pi-lawyer-langgraph.fly.dev' 
      : 'localhost:8000';
    
    const token = session?.access_token;
    if (!token) {
      throw new Error('No authentication token available');
    }
    
    return `${protocol}//${host}/ws/research/${threadId}?token=${encodeURIComponent(token)}`;
  }, [threadId, session?.access_token]);

  // Handle WebSocket messages
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'connection_established':
          console.log('Research WebSocket connected:', message);
          setIsConnected(true);
          setIsConnecting(false);
          setError(null);
          reconnectCountRef.current = 0;
          break;
          
        case 'progress_update':
          const progress = message.progress as ProgressUpdate;
          setProgressUpdates(prev => [...prev, progress]);
          setLatestProgress(progress);
          break;
          
        case 'notification':
          const notification = message.notification as ResearchNotification;
          setNotifications(prev => [...prev, notification]);
          setLatestNotification(notification);
          break;
          
        case 'citation_highlight':
          const citation = message.citation as Citation;
          setCitations(prev => [...prev, citation]);
          break;
          
        case 'pong':
          // Handle ping/pong for keep-alive
          break;
          
        case 'status_update':
          console.log('Research status:', message);
          break;
          
        default:
          console.log('Unknown WebSocket message type:', message.type);
      }
    } catch (err) {
      console.error('Failed to parse WebSocket message:', err);
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }
    
    if (!session?.access_token) {
      setError('No authentication token available');
      return;
    }
    
    try {
      setIsConnecting(true);
      setError(null);
      
      const url = getWebSocketUrl();
      const ws = new WebSocket(url);
      
      ws.onopen = () => {
        console.log('Research WebSocket opened');
        wsRef.current = ws;
      };
      
      ws.onmessage = handleMessage;
      
      ws.onclose = (event) => {
        console.log('Research WebSocket closed:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        wsRef.current = null;
        
        // Attempt reconnection if not a normal closure
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          console.log(`Attempting reconnection ${reconnectCountRef.current}/${reconnectAttempts}`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectDelay * reconnectCountRef.current);
        }
      };
      
      ws.onerror = (event) => {
        console.error('Research WebSocket error:', event);
        setError('WebSocket connection error');
        setIsConnecting(false);
      };
      
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError(err instanceof Error ? err.message : 'Connection failed');
      setIsConnecting(false);
    }
  }, [session?.access_token, getWebSocketUrl, handleMessage, reconnectAttempts, reconnectDelay]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
    reconnectCountRef.current = 0;
  }, []);

  // Send message to WebSocket
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }, []);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setLatestNotification(null);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && session?.access_token && threadId) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [autoConnect, session?.access_token, threadId, connect, disconnect]);

  // Send periodic ping to keep connection alive
  useEffect(() => {
    if (!isConnected) return;
    
    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000); // Ping every 30 seconds
    
    return () => clearInterval(pingInterval);
  }, [isConnected, sendMessage]);

  return {
    // Connection state
    isConnected,
    isConnecting,
    error,
    
    // Data
    progressUpdates,
    citations,
    notifications,
    
    // Actions
    connect,
    disconnect,
    sendMessage,
    clearNotifications,
    
    // Latest updates
    latestProgress,
    latestNotification
  };
}
